const mongoose = require('mongoose');

const fileTransferSchema = new mongoose.Schema({
  // 基本信息
  filename: {
    type: String,
    required: true,
    trim: true
  },
  originalName: {
    type: String,
    required: true,
    trim: true
  },
  fileSize: {
    type: Number,
    required: true,
    min: 0
  },
  mimeType: {
    type: String,
    required: true
  },
  filePath: {
    type: String,
    required: true
  },
  
  // 上传者信息
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  
  // 访问控制
  isPublic: {
    type: Boolean,
    default: false
  },
  password: {
    type: String,
    default: null
  },
  shareToken: {
    type: String,
    unique: true,
    sparse: true
  },
  
  // 过期设置
  expiresAt: {
    type: Date,
    default: function() {
      // 默认24小时后过期
      return new Date(Date.now() + 24 * 60 * 60 * 1000);
    }
  },
  
  // 下载统计
  downloadCount: {
    type: Number,
    default: 0
  },
  lastDownloadAt: {
    type: Date
  },
  
  // 文件状态
  status: {
    type: String,
    enum: ['uploading', 'completed', 'failed', 'deleted'],
    default: 'uploading'
  },
  
  // 分片上传相关
  chunks: [{
    chunkIndex: Number,
    chunkSize: Number,
    uploadedAt: Date,
    checksum: String
  }],
  totalChunks: {
    type: Number,
    default: 1
  },
  
  // 元数据
  metadata: {
    width: Number,      // 图片宽度
    height: Number,     // 图片高度
    duration: Number,   // 音视频时长
    pages: Number       // PDF页数
  }
}, {
  timestamps: true
});

// 索引
fileTransferSchema.index({ uploadedBy: 1, createdAt: -1 });
fileTransferSchema.index({ shareToken: 1 });
fileTransferSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
fileTransferSchema.index({ status: 1 });

// 虚拟字段
fileTransferSchema.virtual('isExpired').get(function() {
  return this.expiresAt && this.expiresAt < new Date();
});

fileTransferSchema.virtual('formattedSize').get(function() {
  const bytes = this.fileSize;
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// 中间件：删除文件时清理磁盘文件
fileTransferSchema.pre('deleteOne', { document: true, query: false }, async function() {
  const fs = require('fs').promises;
  const path = require('path');
  
  try {
    await fs.unlink(this.filePath);
  } catch (error) {
    console.error('删除文件失败:', error);
  }
});

// 静态方法：清理过期文件
fileTransferSchema.statics.cleanupExpiredFiles = async function() {
  const fs = require('fs').promises;
  const expiredFiles = await this.find({
    expiresAt: { $lt: new Date() },
    status: { $ne: 'deleted' }
  });
  
  for (const file of expiredFiles) {
    try {
      await fs.unlink(file.filePath);
      await file.deleteOne();
    } catch (error) {
      console.error('清理过期文件失败:', error);
    }
  }
  
  return expiredFiles.length;
};

// 实例方法：生成分享链接
fileTransferSchema.methods.generateShareToken = function() {
  const crypto = require('crypto');
  this.shareToken = crypto.randomBytes(32).toString('hex');
  return this.shareToken;
};

// 实例方法：增加下载次数
fileTransferSchema.methods.incrementDownload = function() {
  this.downloadCount += 1;
  this.lastDownloadAt = new Date();
  return this.save();
};

module.exports = mongoose.model('FileTransfer', fileTransferSchema);
