'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'FontDownloadOffRound',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M12.58 9.75l-.87-.87l.23-.66h.1l.54 1.53zm-2.23-2.23l.2-.52a1.55 1.55 0 0 1 2.9 0l2.17 5.79L22 19.17V4c0-1.1-.9-2-2-2H4.83l5.52 5.52zm10.84 15.09a.996.996 0 0 1-1.41 0l-.61-.61H4c-1.1 0-2-.9-2-2V4.83l-.61-.61A.996.996 0 1 1 2.8 2.81l18.38 18.38c.4.39.4 1.03.01 1.42zm-9.09-7.68l-3.3-3.3l-1.9 5.07c-.23.63.23 1.3.9 1.3h.01c.41 0 .77-.26.9-.64l.86-2.43h2.53z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
