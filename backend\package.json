{"name": "voiceforge-backend", "version": "1.0.0", "description": "VoiceForge后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:email": "node scripts/test-email.js", "simple": "node simple-server.js", "simple:dev": "nodemon simple-server.js", "db:init": "node scripts/init-database.js", "db:backup": "node scripts/backup-database.js", "db:restore": "node scripts/backup-database.js restore", "user:list": "node scripts/list-users.js", "user:reset": "node scripts/reset-user-password.js", "admin:reset": "node scripts/reset-admin-password.js", "files:cleanup": "node scripts/cleanup-expired-files.js", "files:cleanup:daemon": "node scripts/cleanup-expired-files.js --daemon"}, "keywords": ["voiceforge", "ai", "voice", "synthesis"], "author": "VoiceForge Team", "license": "MIT", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "rate-limit-mongo": "^2.3.2", "redis": "^4.6.11", "response-time": "^2.3.2", "uuid": "^9.0.1", "winston": "^3.11.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=18.0.0"}}