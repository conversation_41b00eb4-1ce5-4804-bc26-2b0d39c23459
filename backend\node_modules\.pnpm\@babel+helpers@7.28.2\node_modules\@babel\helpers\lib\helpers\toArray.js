"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _toArray;
var _arrayWithHoles = require("./arrayWithHoles.js");
var _iterableToArray = require("./iterableToArray.js");
var _unsupportedIterableToArray = require("./unsupportedIterableToArray.js");
var _nonIterableRest = require("./nonIterableRest.js");
function _toArray(arr) {
  return (0, _arrayWithHoles.default)(arr) || (0, _iterableToArray.default)(arr) || (0, _unsupportedIterableToArray.default)(arr) || (0, _nonIterableRest.default)();
}

//# sourceMappingURL=toArray.js.map
