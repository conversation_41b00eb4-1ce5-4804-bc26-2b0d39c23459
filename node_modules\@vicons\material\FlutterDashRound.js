'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'FlutterDashRound',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M11.07 11.7c.29-.39.81-.56 1.27-.37c.17.07.32.18.43.33c.22.28.25.59.22.85c-.05.33-.25.63-.54.79c0 0-4.87 2.95-5.07 2.69s3.69-4.29 3.69-4.29zM22 10c0 2.5-1 3-1.5 3c-.23 0-.44-.1-.62-.26c-.48 3.32-2.36 5.31-5.33 5.99c.11.44.48.77.95.77h.58c.22 0 .41.15.48.36c.17.52.66 1.02 1.02 1.32c.25.21.24.59-.03.78c-.34.24-.9.49-1.79.53a.501.501 0 0 1-.45-.22c-.13-.2-.31-.56-.31-1.01c0-.3.04-.57.09-.8c-.78-.16-1.39-.78-1.55-1.56c-.49.06-1 .1-1.54.1c-.88 0-1.7-.09-2.45-.25c-.02.08-.05.16-.05.25c0 .55.45 1 1 1h.58c.22 0 .41.15.48.36c.17.52.66 1.02 1.02 1.32c.25.21.24.59-.03.78c-.34.24-.9.49-1.79.53a.501.501 0 0 1-.45-.22c-.13-.2-.31-.56-.31-1.01c0-.3.04-.57.09-.8c-.9-.19-1.59-1-1.59-1.96c0-.18.03-.36.08-.53c-2.46-.86-4.03-2.78-4.46-5.74c-.18.17-.38.27-.62.27C3 13 2 12.5 2 10c0-2.27 1.7-4.5 3-4.5c.43 0 .49.49.5.85a7.989 7.989 0 0 1 5.55-3.29c.2-.96 1.08-1.56 1.95-1.56v1s.33-.5 1-.5c.67 0 1 .5 1 .5c-.49 0-.85.35-.96.77c1.82.48 3.39 1.59 4.46 3.08c.01-.36.07-.85.5-.85c1.3 0 3 2.23 3 4.5zM5 11c0 .81.1 1.53.25 2.21c.18-.69.46-1.33.83-1.92a3.77 3.77 0 0 1-.34-1.54A3.766 3.766 0 0 1 9.5 6c.96 0 1.84.37 2.5.97c.66-.6 1.54-.97 2.5-.97c2.07 0 3.75 1.68 3.75 3.75c0 .55-.12 1.07-.34 1.54c.37.59.66 1.24.84 1.94c.15-.68.25-1.41.25-2.23c0-3.86-3.14-7-7-7s-7 3.14-7 7zm12.98 4.29c0-.1.02-.19.02-.29c0-1.01-.26-1.95-.7-2.78c-.69.78-1.68 1.28-2.8 1.28c-.27 0-.54-.03-.79-.09c.14-.23.23-.49.27-.77c.01-.07.01-.13.02-.19c.17.03.33.05.5.05c1.52 0 2.75-1.23 2.75-2.75S16.02 7 14.5 7c-.67 0-1.32.25-1.83.72l-.67.6l-.67-.6C10.82 7.25 10.17 7 9.5 7C7.98 7 6.75 8.23 6.75 9.75c0 1.34.96 2.46 2.23 2.7l-.76.83c-.6-.22-1.12-.59-1.53-1.05A6.01 6.01 0 0 0 6 15c0 .08.01.15.01.24C7.13 17.06 9.14 18 12 18c2.88 0 4.88-.91 5.98-2.71zM16 9.75c0 .97-.67 1.75-1.5 1.75S13 10.72 13 9.75S13.67 8 14.5 8s1.5.78 1.5 1.75zm-.75-.87c0-.21-.17-.38-.38-.38s-.37.17-.37.38s.17.38.38.38s.37-.18.37-.38zM11 9.75c0 .97-.67 1.75-1.5 1.75S8 10.72 8 9.75S8.67 8 9.5 8s1.5.78 1.5 1.75zm-.75-.87c0-.21-.17-.38-.38-.38s-.37.17-.37.38s.17.38.38.38s.37-.18.37-.38z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
