@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置颜色代码
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

echo %BLUE%=== 强制文件删除工具 ===%RESET%
echo %BLUE%当前工作目录: %CD%%RESET%
echo.

:show_help
echo %BLUE%=== 使用说明 ===%RESET%
echo 1. 输入要删除的文件或文件夹路径
echo 2. 支持相对路径和绝对路径
echo 3. 输入 "help" 显示帮助
echo 4. 输入 "exit" 退出程序
echo %RED%注意: 此工具会强制删除文件，请谨慎使用！%RESET%
echo.

:main_loop
set /p "input=请输入要删除的文件/文件夹路径 (或输入命令): "

if /i "!input!"=="exit" (
    echo %GREEN%再见！%RESET%
    goto :end
)

if /i "!input!"=="help" (
    goto :show_help
)

if "!input!"=="" (
    echo %YELLOW%请输入有效路径%RESET%
    goto :main_loop
)

:: 检查路径是否存在
if not exist "!input!" (
    echo %YELLOW%路径不存在: !input!%RESET%
    goto :main_loop
)

:: 获取绝对路径
for %%i in ("!input!") do set "absolute_path=%%~fi"

:: 检查是文件还是文件夹
if exist "!absolute_path!\*" (
    set "item_type=文件夹"
) else (
    set "item_type=文件"
)

echo.
echo %BLUE%准备删除: !absolute_path!%RESET%
echo %BLUE%类型: !item_type!%RESET%

:: 确认删除
set /p "confirm=确认删除吗？(y/N): "
if /i not "!confirm!"=="y" if /i not "!confirm!"=="yes" (
    echo %YELLOW%取消删除操作%RESET%
    goto :main_loop
)

echo.
echo %BLUE%开始删除...%RESET%

:: 执行删除操作
if exist "!absolute_path!\*" (
    :: 删除文件夹
    call :delete_directory "!absolute_path!"
) else (
    :: 删除文件
    call :delete_file "!absolute_path!"
)

goto :main_loop

:delete_file
set "file_path=%~1"
:: 移除只读属性
attrib -r "!file_path!" 2>nul
:: 强制删除文件
del /f /q "!file_path!" 2>nul
if !errorlevel! equ 0 (
    echo %GREEN%✓ 成功删除文件: !file_path!%RESET%
) else (
    echo %RED%✗ 删除文件失败: !file_path!%RESET%
    :: 尝试使用takeown获取所有权
    echo %YELLOW%尝试获取文件所有权...%RESET%
    takeown /f "!file_path!" >nul 2>&1
    icacls "!file_path!" /grant %username%:F >nul 2>&1
    del /f /q "!file_path!" 2>nul
    if !errorlevel! equ 0 (
        echo %GREEN%✓ 获取所有权后成功删除: !file_path!%RESET%
    ) else (
        echo %RED%✗ 最终删除失败: !file_path!%RESET%
    )
)
goto :eof

:delete_directory
set "dir_path=%~1"
:: 移除只读属性
attrib -r "!dir_path!" /s /d 2>nul
:: 强制删除文件夹
rmdir /s /q "!dir_path!" 2>nul
if !errorlevel! equ 0 (
    echo %GREEN%✓ 成功删除文件夹: !dir_path!%RESET%
) else (
    echo %RED%✗ 删除文件夹失败: !dir_path!%RESET%
    :: 尝试使用takeown获取所有权
    echo %YELLOW%尝试获取文件夹所有权...%RESET%
    takeown /f "!dir_path!" /r /d y >nul 2>&1
    icacls "!dir_path!" /grant %username%:F /t >nul 2>&1
    rmdir /s /q "!dir_path!" 2>nul
    if !errorlevel! equ 0 (
        echo %GREEN%✓ 获取所有权后成功删除: !dir_path!%RESET%
    ) else (
        echo %RED%✗ 最终删除失败: !dir_path!%RESET%
        echo %YELLOW%建议以管理员身份运行此程序%RESET%
    )
)
goto :eof

:end
pause
