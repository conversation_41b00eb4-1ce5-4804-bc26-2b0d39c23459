# 强制文件删除工具 - PowerShell版本
# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 颜色函数
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

# 强制删除文件函数
function Remove-FileForce {
    param([string]$FilePath)
    
    try {
        # 移除只读属性
        if (Test-Path $FilePath) {
            Set-ItemProperty -Path $FilePath -Name IsReadOnly -Value $false -ErrorAction SilentlyContinue
        }
        
        # 删除文件
        Remove-Item -Path $FilePath -Force -ErrorAction Stop
        Write-ColorText "✓ 成功删除文件: $FilePath" "Green"
        return $true
    }
    catch {
        Write-ColorText "✗ 删除文件失败: $FilePath - $($_.Exception.Message)" "Red"
        
        # 尝试获取所有权
        try {
            Write-ColorText "尝试获取文件所有权..." "Yellow"
            takeown /f "$FilePath" | Out-Null
            icacls "$FilePath" /grant "${env:USERNAME}:F" | Out-Null
            Remove-Item -Path $FilePath -Force -ErrorAction Stop
            Write-ColorText "✓ 获取所有权后成功删除: $FilePath" "Green"
            return $true
        }
        catch {
            Write-ColorText "✗ 最终删除失败: $FilePath" "Red"
            return $false
        }
    }
}

# 强制删除文件夹函数
function Remove-DirectoryForce {
    param([string]$DirectoryPath)
    
    try {
        # 递归移除只读属性
        Get-ChildItem -Path $DirectoryPath -Recurse -Force | ForEach-Object {
            Set-ItemProperty -Path $_.FullName -Name IsReadOnly -Value $false -ErrorAction SilentlyContinue
        }
        
        # 删除文件夹
        Remove-Item -Path $DirectoryPath -Recurse -Force -ErrorAction Stop
        Write-ColorText "✓ 成功删除文件夹: $DirectoryPath" "Green"
        return $true
    }
    catch {
        Write-ColorText "✗ 删除文件夹失败: $DirectoryPath - $($_.Exception.Message)" "Red"
        
        # 尝试获取所有权
        try {
            Write-ColorText "尝试获取文件夹所有权..." "Yellow"
            takeown /f "$DirectoryPath" /r /d y | Out-Null
            icacls "$DirectoryPath" /grant "${env:USERNAME}:F" /t | Out-Null
            Remove-Item -Path $DirectoryPath -Recurse -Force -ErrorAction Stop
            Write-ColorText "✓ 获取所有权后成功删除: $DirectoryPath" "Green"
            return $true
        }
        catch {
            Write-ColorText "✗ 最终删除失败: $DirectoryPath" "Red"
            Write-ColorText "建议以管理员身份运行PowerShell" "Yellow"
            return $false
        }
    }
}

# 删除路径函数
function Remove-PathForce {
    param([string]$TargetPath)
    
    # 获取绝对路径
    $AbsolutePath = Resolve-Path -Path $TargetPath -ErrorAction SilentlyContinue
    if (-not $AbsolutePath) {
        $AbsolutePath = $TargetPath
    }
    
    if (-not (Test-Path $TargetPath)) {
        Write-ColorText "路径不存在: $AbsolutePath" "Yellow"
        return $false
    }
    
    $Item = Get-Item -Path $TargetPath
    $ItemType = if ($Item.PSIsContainer) { "文件夹" } else { "文件" }
    
    Write-Host ""
    Write-ColorText "准备删除: $($Item.FullName)" "Cyan"
    Write-ColorText "类型: $ItemType" "Cyan"
    
    if (-not $Item.PSIsContainer) {
        $SizeKB = [math]::Round($Item.Length / 1024, 2)
        Write-ColorText "大小: $SizeKB KB" "Cyan"
    }
    
    # 确认删除
    $Confirm = Read-Host "确认删除吗？(y/N)"
    if ($Confirm -notin @('y', 'Y', 'yes', 'Yes', 'YES')) {
        Write-ColorText "取消删除操作" "Yellow"
        return $false
    }
    
    Write-Host ""
    Write-ColorText "开始删除..." "Cyan"
    
    if ($Item.PSIsContainer) {
        return Remove-DirectoryForce -DirectoryPath $Item.FullName
    } else {
        return Remove-FileForce -FilePath $Item.FullName
    }
}

# 批量删除函数
function Remove-PathsBatch {
    param([string[]]$Paths)
    
    Write-ColorText "`n=== 批量删除模式 ===" "Cyan"
    $SuccessCount = 0
    $FailCount = 0
    
    foreach ($Path in $Paths) {
        $Path = $Path.Trim()
        if ($Path) {
            $Result = Remove-PathForce -TargetPath $Path
            if ($Result) {
                $SuccessCount++
            } else {
                $FailCount++
            }
            Write-Host ""
        }
    }
    
    Write-ColorText "=== 删除完成 ===" "Cyan"
    Write-ColorText "成功: $SuccessCount 个" "Green"
    Write-ColorText "失败: $FailCount 个" "Red"
}

# 显示帮助信息
function Show-Help {
    Write-ColorText "`n=== 文件删除工具使用说明 ===" "Cyan"
    Write-Host "1. 单个删除: 直接输入文件或文件夹路径"
    Write-Host "2. 批量删除: 输入多个路径，用分号(;)分隔"
    Write-Host "3. 相对路径: 相对于当前目录"
    Write-Host "4. 绝对路径: 完整路径"
    Write-Host "5. 输入 'help' 显示帮助"
    Write-Host "6. 输入 'exit' 退出程序"
    Write-ColorText "`n注意: 此工具会强制删除文件，请谨慎使用！" "Red"
}

# 主程序
function Main {
    Write-ColorText "=== 强制文件删除工具 ===" "Cyan"
    Write-ColorText "当前工作目录: $(Get-Location)" "Cyan"
    Show-Help
    
    while ($true) {
        Write-Host ""
        $Input = Read-Host "请输入要删除的文件/文件夹路径 (或输入命令)"
        
        $TrimmedInput = $Input.Trim()
        
        if ($TrimmedInput -eq "exit") {
            Write-ColorText "再见！" "Green"
            break
        }
        
        if ($TrimmedInput -eq "help") {
            Show-Help
            continue
        }
        
        if ($TrimmedInput -eq "") {
            Write-ColorText "请输入有效路径" "Yellow"
            continue
        }
        
        # 检查是否为批量删除（包含分号）
        if ($TrimmedInput.Contains(";")) {
            $Paths = $TrimmedInput.Split(";") | Where-Object { $_.Trim() }
            Remove-PathsBatch -Paths $Paths
        } else {
            Remove-PathForce -TargetPath $TrimmedInput
        }
    }
}

# 错误处理
trap {
    Write-ColorText "程序错误: $($_.Exception.Message)" "Red"
    Read-Host "按回车键退出"
    exit 1
}

# 启动程序
Main
