const { v4: uuidv4 } = require('uuid');
const computationTimeManager = require('../services/membershipTimeManager');
const sessionManager = require('../services/sessionManager');
const { logger } = require('../utils/logger');

/**
 * 算力消耗跟踪中间件
 * 用于跟踪和管理用户的算力消耗任务
 */

/**
 * 开始算力消耗跟踪
 * 在语音合成等算力密集型任务开始时调用
 */
function startComputationTracking(req, res, next) {
  try {
    // 生成任务ID
    const taskId = uuidv4();

    // 从请求中获取用户信息
    const userId = req.user?.userId || req.user?.id;
    if (!userId) {
      logger.warn('无法获取用户ID，跳过算力跟踪');
      return next();
    }

    // 获取会话ID
    const sessionId = req.user?.sessionId;
    if (!sessionId) {
      logger.warn('无法获取会话ID，跳过算力跟踪');
      return next();
    }

    // 确定任务类型
    const taskType = determineTaskType(req);

    // 开始跟踪算力消耗（包含会话ID）
    computationTimeManager.startComputation(userId, taskId, sessionId, taskType);

    // 将任务ID添加到请求对象中，供后续使用
    req.computationTaskId = taskId;
    req.computationStartTime = new Date();

    logger.debug('算力消耗跟踪开始', {
      userId,
      taskId,
      sessionId,
      taskType,
      endpoint: req.originalUrl
    });

    next();

  } catch (error) {
    logger.error('启动算力跟踪失败', { error: error.message });
    next(); // 继续执行，不阻塞请求
  }
}

/**
 * 结束算力消耗跟踪
 * 在任务完成时调用
 */
function endComputationTracking(req, res, next) {
  try {
    const taskId = req.computationTaskId;
    if (taskId) {
      computationTimeManager.endComputation(taskId);

      const duration = new Date() - req.computationStartTime;
      logger.debug('算力消耗跟踪结束', {
        taskId,
        duration: `${duration}ms`,
        endpoint: req.originalUrl
      });
    }

    next();

  } catch (error) {
    logger.error('结束算力跟踪失败', { error: error.message });
    next();
  }
}

/**
 * 算力消耗跟踪装饰器
 * 自动处理开始和结束跟踪
 */
function withComputationTracking(handler) {
  return async (req, res, next) => {
    // 开始跟踪
    startComputationTracking(req, res, () => {});
    
    try {
      // 执行原始处理器
      await handler(req, res, next);
      
    } finally {
      // 结束跟踪
      endComputationTracking(req, res, () => {});
    }
  };
}

/**
 * 确定任务类型
 */
function determineTaskType(req) {
  const path = req.originalUrl.toLowerCase();
  
  if (path.includes('/voice/synthesize') || path.includes('/tts')) {
    return 'voice_synthesis';
  } else if (path.includes('/voice/clone')) {
    return 'voice_clone';
  } else if (path.includes('/audio/process')) {
    return 'audio_processing';
  } else if (path.includes('/ai/') || path.includes('/generate')) {
    return 'ai_generation';
  } else {
    return 'unknown_computation';
  }
}

/**
 * 检查用户算力消耗状态的中间件
 */
function checkComputationStatus(req, res, next) {
  try {
    const userId = req.user?.userId || req.user?.id;
    if (!userId) {
      return next();
    }

    const stats = computationTimeManager.getUserComputationStats(userId);

    // 将算力状态添加到响应头中
    res.set('X-Computation-Active', stats.activeCount > 0 ? 'true' : 'false');
    res.set('X-Computation-Count', stats.activeCount.toString());

    // 如果有活跃算力消耗，记录到请求对象中
    if (stats.activeCount > 0) {
      req.hasActiveComputation = true;
      req.activeComputationCount = stats.activeCount;
    }

    next();

  } catch (error) {
    logger.error('检查算力状态失败', { error: error.message });
    next();
  }
}

/**
 * 算力消耗限制中间件
 * 防止用户同时启动过多算力任务
 */
function limitConcurrentComputations(maxConcurrent = 3) {
  return (req, res, next) => {
    try {
      const userId = req.user?.userId || req.user?.id;
      if (!userId) {
        return next();
      }

      const stats = computationTimeManager.getUserComputationStats(userId);

      if (stats.activeCount >= maxConcurrent) {
        return res.status(429).json({
          success: false,
          message: `同时进行的算力任务过多，请等待当前任务完成。当前: ${stats.activeCount}/${maxConcurrent}`,
          activeComputations: stats.activeCount,
          maxAllowed: maxConcurrent
        });
      }

      next();

    } catch (error) {
      logger.error('算力限制检查失败', { error: error.message });
      next();
    }
  };
}

/**
 * 会员资格验证中间件
 * 检查用户是否有足够的会员时间进行算力消耗
 */
function validateMembershipForComputation(req, res, next) {
  // 这个中间件可以在需要时实现
  // 检查用户会员时间是否足够
  next();
}

/**
 * 获取用户算力统计的路由处理器
 */
function getComputationStats(req, res) {
  try {
    const userId = req.user?.userId || req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      });
    }

    const userStats = computationTimeManager.getUserComputationStats(userId);
    const systemStats = computationTimeManager.getSystemStats();

    res.json({
      success: true,
      data: {
        user: userStats,
        system: systemStats
      }
    });

  } catch (error) {
    logger.error('获取算力统计失败', { error: error.message });
    res.status(500).json({
      success: false,
      message: '获取统计信息失败'
    });
  }
}

/**
 * 强制结束用户所有算力任务的路由处理器
 */
function forceEndUserComputations(req, res) {
  try {
    const userId = req.user?.userId || req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '未授权访问'
      });
    }

    // 结束用户所有活跃的算力任务
    const endedCount = computationTimeManager.endAllUserComputations(userId);

    res.json({
      success: true,
      message: `已强制结束 ${endedCount} 个算力任务`,
      endedTasks: endedCount
    });

  } catch (error) {
    logger.error('强制结束算力任务失败', { error: error.message });
    res.status(500).json({
      success: false,
      message: '操作失败'
    });
  }
}

module.exports = {
  startComputationTracking,
  endComputationTracking,
  withComputationTracking,
  checkComputationStatus,
  limitConcurrentComputations,
  validateMembershipForComputation,
  getComputationStats,
  forceEndUserComputations
};
