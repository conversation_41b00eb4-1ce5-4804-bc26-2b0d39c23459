const mongoose = require('mongoose');

const fileChunkSchema = new mongoose.Schema({
  // 文件标识
  fileId: {
    type: String,
    required: true,
    index: true
  },
  fileName: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number,
    required: true
  },
  fileMd5: {
    type: String,
    required: true,
    index: true
  },
  
  // 分片信息
  chunkIndex: {
    type: Number,
    required: true
  },
  chunkSize: {
    type: Number,
    required: true
  },
  chunkMd5: {
    type: String,
    required: true
  },
  totalChunks: {
    type: Number,
    required: true
  },
  
  // 上传者信息
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 状态信息
  status: {
    type: String,
    enum: ['uploading', 'completed', 'failed'],
    default: 'uploading'
  },
  
  // 存储路径
  chunkPath: {
    type: String,
    required: true
  },
  
  // 时间戳
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  
  // 过期时间（24小时后清理未完成的分片）
  expiresAt: {
    type: Date,
    default: function() {
      return new Date(Date.now() + 24 * 60 * 60 * 1000);
    }
  }
}, {
  timestamps: true
});

// 复合索引
fileChunkSchema.index({ fileId: 1, chunkIndex: 1 }, { unique: true });
fileChunkSchema.index({ uploadedBy: 1, createdAt: -1 });
fileChunkSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// 静态方法：获取文件的上传进度
fileChunkSchema.statics.getUploadProgress = async function(fileId) {
  const chunks = await this.find({ fileId, status: 'completed' });
  const totalChunks = chunks.length > 0 ? chunks[0].totalChunks : 0;
  const completedChunks = chunks.length;
  
  return {
    completedChunks,
    totalChunks,
    progress: totalChunks > 0 ? Math.round((completedChunks / totalChunks) * 100) : 0,
    isComplete: completedChunks === totalChunks && totalChunks > 0
  };
};

// 静态方法：检查文件是否已存在
fileChunkSchema.statics.checkFileExists = async function(fileMd5, uploadedBy) {
  const FileTransfer = require('./FileTransfer');
  
  // 检查是否已有完整文件
  const existingFile = await FileTransfer.findOne({
    uploadedBy,
    status: 'completed'
  });
  
  if (existingFile) {
    // 这里可以添加MD5校验逻辑
    return existingFile;
  }
  
  return null;
};

// 静态方法：合并分片
fileChunkSchema.statics.mergeChunks = async function(fileId) {
  const fs = require('fs').promises;
  const path = require('path');
  const crypto = require('crypto');
  
  try {
    // 获取所有分片
    const chunks = await this.find({ 
      fileId, 
      status: 'completed' 
    }).sort({ chunkIndex: 1 });
    
    if (chunks.length === 0) {
      throw new Error('没有找到已完成的分片');
    }
    
    const firstChunk = chunks[0];
    const outputDir = path.join(__dirname, '../uploads/file-transfer');
    const outputPath = path.join(outputDir, `${fileId}_merged`);
    
    // 确保输出目录存在
    await fs.mkdir(outputDir, { recursive: true });
    
    // 合并分片
    const writeStream = require('fs').createWriteStream(outputPath);
    
    for (const chunk of chunks) {
      const chunkData = await fs.readFile(chunk.chunkPath);
      writeStream.write(chunkData);
    }
    
    writeStream.end();
    
    // 等待写入完成
    await new Promise((resolve, reject) => {
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });
    
    // 验证合并后的文件
    const mergedData = await fs.readFile(outputPath);
    const mergedMd5 = crypto.createHash('md5').update(mergedData).digest('hex');
    
    if (mergedMd5 !== firstChunk.fileMd5) {
      throw new Error('文件合并后MD5校验失败');
    }
    
    // 清理分片文件
    for (const chunk of chunks) {
      try {
        await fs.unlink(chunk.chunkPath);
      } catch (error) {
        console.error('清理分片文件失败:', error);
      }
    }
    
    // 删除分片记录
    await this.deleteMany({ fileId });
    
    return {
      filePath: outputPath,
      fileSize: mergedData.length,
      fileMd5: mergedMd5
    };
    
  } catch (error) {
    console.error('合并分片失败:', error);
    throw error;
  }
};

// 静态方法：清理过期分片
fileChunkSchema.statics.cleanupExpiredChunks = async function() {
  const fs = require('fs').promises;
  
  const expiredChunks = await this.find({
    expiresAt: { $lt: new Date() }
  });
  
  for (const chunk of expiredChunks) {
    try {
      await fs.unlink(chunk.chunkPath);
    } catch (error) {
      console.error('清理过期分片文件失败:', error);
    }
  }
  
  const result = await this.deleteMany({
    expiresAt: { $lt: new Date() }
  });
  
  return result.deletedCount;
};

module.exports = mongoose.model('FileChunk', fileChunkSchema);
