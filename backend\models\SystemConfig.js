const mongoose = require('mongoose');

// 系统配置模型
const systemConfigSchema = new mongoose.Schema({
  // 配置键名（唯一标识）
  key: {
    type: String,
    required: [true, '配置键不能为空'],
    unique: true,
    trim: true,
    index: true
  },
  
  // 配置值
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, '配置值不能为空']
  },
  
  // 配置类型
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    required: true
  },
  
  // 配置分类
  category: {
    type: String,
    enum: [
      'system',      // 系统基础配置
      'security',    // 安全配置
      'email',       // 邮件配置
      'payment',     // 支付配置
      'ai',          // AI服务配置
      'storage',     // 存储配置
      'ui',          // 界面配置
      'feature'      // 功能开关
    ],
    required: true,
    index: true
  },
  
  // 配置描述
  description: {
    type: String,
    required: [true, '配置描述不能为空'],
    maxlength: 500
  },
  
  // 是否为敏感配置（如密码、密钥等）
  sensitive: {
    type: Boolean,
    default: false
  },
  
  // 是否可以通过API修改
  editable: {
    type: Boolean,
    default: true
  },
  
  // 配置的默认值
  defaultValue: {
    type: mongoose.Schema.Types.Mixed
  },
  
  // 配置验证规则
  validation: {
    required: { type: Boolean, default: false },
    min: Number,
    max: Number,
    pattern: String,
    enum: [String]
  },
  
  // 最后修改者
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // 修改历史（保留最近10次）
  history: [{
    value: mongoose.Schema.Types.Mixed,
    modifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    modifiedAt: {
      type: Date,
      default: Date.now
    },
    reason: String
  }]

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
systemConfigSchema.index({ category: 1, key: 1 });
systemConfigSchema.index({ editable: 1 });
systemConfigSchema.index({ sensitive: 1 });

// 虚拟字段
systemConfigSchema.virtual('isDefault').get(function() {
  return JSON.stringify(this.value) === JSON.stringify(this.defaultValue);
});

// 实例方法
systemConfigSchema.methods.updateValue = function(newValue, userId, reason) {
  // 添加到历史记录
  this.history.unshift({
    value: this.value,
    modifiedBy: userId,
    modifiedAt: new Date(),
    reason: reason || '配置更新'
  });
  
  // 只保留最近10次历史
  if (this.history.length > 10) {
    this.history = this.history.slice(0, 10);
  }
  
  // 更新当前值
  this.value = newValue;
  this.lastModifiedBy = userId;
  
  return this.save();
};

systemConfigSchema.methods.resetToDefault = function(userId, reason) {
  if (this.defaultValue !== undefined) {
    return this.updateValue(this.defaultValue, userId, reason || '重置为默认值');
  }
  throw new Error('该配置没有默认值');
};

// 静态方法
systemConfigSchema.statics.getByKey = function(key) {
  return this.findOne({ key });
};

systemConfigSchema.statics.getByCategory = function(category) {
  return this.find({ category }).sort({ key: 1 });
};

systemConfigSchema.statics.getPublicConfigs = function() {
  return this.find({ sensitive: false }).select('-history');
};

systemConfigSchema.statics.createOrUpdate = async function(key, value, options = {}) {
  const config = await this.findOne({ key });
  
  if (config) {
    return config.updateValue(value, options.userId, options.reason);
  } else {
    return this.create({
      key,
      value,
      type: options.type || typeof value,
      category: options.category || 'system',
      description: options.description || '系统配置',
      sensitive: options.sensitive || false,
      editable: options.editable !== false,
      defaultValue: options.defaultValue || value,
      validation: options.validation || {},
      lastModifiedBy: options.userId
    });
  }
};

// 中间件
systemConfigSchema.pre('save', function(next) {
  // 验证配置值类型
  const actualType = Array.isArray(this.value) ? 'array' : typeof this.value;
  if (actualType === 'object' && this.value !== null && !Array.isArray(this.value)) {
    this.type = 'object';
  } else if (actualType !== this.type && this.type !== 'object') {
    return next(new Error(`配置值类型不匹配，期望: ${this.type}, 实际: ${actualType}`));
  }
  next();
});

// 删除敏感信息的方法
systemConfigSchema.methods.toSafeObject = function() {
  const configObject = this.toObject();
  if (this.sensitive) {
    configObject.value = '***';
    delete configObject.history;
  }
  return configObject;
};

module.exports = mongoose.model('SystemConfig', systemConfigSchema);
