'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'Forward5Sharp',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M17.95 13c0 3.31-2.69 6-6 6s-6-2.69-6-6s2.69-6 6-6v4l5-5l-5-5v4c-4.42 0-8 3.58-8 8s3.58 8 8 8s8-3.58 8-8h-2zm-5.52 2.15c-.05.07-.11.13-.18.17s-.17.06-.27.06c-.17 0-.31-.05-.42-.15s-.17-.24-.19-.41h-.84c.01.2.05.37.13.53s.19.28.32.39s.29.19.46.24s.35.08.53.08c.24 0 .46-.04.64-.12s.33-.18.45-.31s.21-.28.27-.45s.09-.35.09-.54c0-.22-.03-.43-.09-.6s-.14-.33-.25-.45s-.25-.22-.41-.28s-.34-.1-.55-.1c-.07 0-.14.01-.2.02s-.13.02-.18.04s-.1.03-.15.05s-.08.04-.11.05l.11-.92h1.7v-.71H10.9l-.25 2.17l.67.17c.03-.03.06-.06.1-.09s.07-.05.12-.07s.1-.04.15-.05s.13-.02.2-.02c.12 0 .22.02.3.05s.16.09.21.15s.1.14.13.24s.04.19.04.31s-.01.22-.03.31s-.06.17-.11.24z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
