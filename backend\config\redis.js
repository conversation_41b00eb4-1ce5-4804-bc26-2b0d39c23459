const redis = require('redis');
const { logger } = require('../utils/logger');

let client = null;

const connectRedis = async () => {
  try {
    // 检查是否需要Redis
    if (process.env.DISABLE_REDIS === 'true') {
      logger.info('Redis已禁用，跳过连接');
      return;
    }

    const redisConfig = {
      url: `redis://${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || 6379}`,
      socket: {
        connectTimeout: 3000,
        commandTimeout: 3000,
        reconnectStrategy: false // 禁用自动重连
      }
    };

    // 如果有密码，添加密码配置
    if (process.env.REDIS_PASSWORD) {
      redisConfig.password = process.env.REDIS_PASSWORD;
    }

    client = redis.createClient(redisConfig);

    // 错误处理 - 只记录一次错误
    client.on('error', (err) => {
      if (err.code === 'ECONNREFUSED') {
        logger.warn('Redis服务未启动，应用将在没有Redis的情况下运行');
        client = null;
      } else {
        logger.error('Redis连接错误:', err.message);
      }
    });

    client.on('connect', () => {
      logger.info('Redis连接成功');
    });

    client.on('ready', () => {
      logger.info('Redis准备就绪');
    });

    // 尝试连接，设置超时
    const connectPromise = client.connect();
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('连接超时')), 3000);
    });

    await Promise.race([connectPromise, timeoutPromise]);

  } catch (error) {
    logger.warn('Redis连接失败，应用将在没有Redis的情况下运行:', error.message);
    client = null;
    // 不抛出错误，让应用继续运行
  }
};

// 获取Redis客户端
const getRedisClient = () => {
  if (!client || !client.isOpen) {
    logger.warn('Redis客户端未连接');
    return null;
  }
  return client;
};

// 优雅关闭Redis连接
const closeRedis = async () => {
  if (client && client.isOpen) {
    await client.quit();
    logger.info('Redis连接已关闭');
  }
};

module.exports = {
  connectRedis,
  getRedisClient,
  closeRedis
};
