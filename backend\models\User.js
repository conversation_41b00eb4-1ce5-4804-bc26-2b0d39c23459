const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  // 基本信息
  username: {
    type: String,
    required: [true, '用户名不能为空'],
    unique: true,
    trim: true,
    minlength: [3, '用户名至少需要3个字符'],
    maxlength: [30, '用户名不能超过30个字符'],
    match: [/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, '用户名只能包含字母、数字、下划线和中文'],
    index: true
  },
  
  email: {
    type: String,
    required: [true, '邮箱不能为空'],
    lowercase: true,
    trim: true,
    match: [/^\S+@\S+\.\S+$/, '请输入有效的邮箱地址'],
    index: true
  },

  // 微信账号 - 用于用户沟通（可选）
  wechatId: {
    type: String,
    required: false, // 改为可选
    trim: true,
    minlength: [6, '微信号错误请输入正确的微信号'],
    maxlength: [20, '微信号错误请输入正确的微信号'],
    match: [/^[a-zA-Z_-][a-zA-Z0-9_-]{5,19}$/, '微信号错误请输入正确的微信号'],
    index: true
  },
  
  password: {
    type: String,
    required: [true, '密码不能为空'],
    minlength: [6, '密码至少需要6个字符'],
    select: false // 默认查询时不返回密码
  },
  
  // 用户状态
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    default: 'active'
  },
  
  emailVerified: {
    type: Boolean,
    default: false
  },
  
  emailVerifiedAt: {
    type: Date
  },
  
  // 用户角色和权限
  role: {
    type: String,
    enum: ['user', 'member', 'agent', 'admin', 'administrator'],
    default: 'user'
  },
  
  permissions: [{
    type: String,
    enum: [
      'voice_synthesis',      // 语音合成
      'voice_clone',          // 声音克隆
      'batch_processing',     // 批量处理
      'api_access',           // API访问
      'admin_panel',          // 管理面板
      '代理控制',              // 代理人面板
      'user_management',      // 用户管理
      'system_settings'       // 系统设置
    ]
  }],
  
  // 使用限制
  limits: {
    dailyVoiceMinutes: { type: Number, default: 10 },    // 每日语音+画面分钟数
    monthlyVoiceMinutes: { type: Number, default: 300 }, // 每月语音+画面分钟数
    maxFileSize: { type: Number, default: 10 },          // 最大文件大小(MB)
    concurrentJobs: { type: Number, default: 1 }         // 并发任务数
  },
  
  // 会员信息
  membership: {
    type: {
      type: String,
      enum: ['free', 'basic', 'premium', 'enterprise'],
      default: 'free'
    },
    expiresAt: Date,
    autoRenew: { type: Boolean, default: false }
  },

  // 前端兼容字段
  membershipLevel: {
    type: String,
    enum: ['none', 'basic', 'premium', 'enterprise'],
    default: 'none'
  },
  membershipEndDate: Date,
  cloudComputingEndDate: Date,

  // 新增时间字段
  voiceTimeEndDate: Date,    // 语音时长结束时间
  videoTimeEndDate: Date,    // 视频时长结束时间

  // Agent收益记录
  agentEarnings: {
    totalVoiceMinutes: { type: Number, default: 0 },    // 累计获得的语音时长(分钟)
    totalVideoMinutes: { type: Number, default: 0 },    // 累计获得的视频时长(分钟)
    totalEarnings: { type: Number, default: 0 },        // 总收益次数
    lastEarningDate: Date,                              // 最后一次获得收益时间
    earningHistory: [{                                  // 收益历史记录
      cardType: { type: String, enum: ['voice', 'video', 'voice-year', 'video-year'] },
      earnedMinutes: Number,                            // 获得的分钟数
      fromUserId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // 来源用户
      cardKeyId: { type: mongoose.Schema.Types.ObjectId, ref: 'CardKey' }, // 卡密ID
      earnedAt: { type: Date, default: Date.now }       // 获得时间
    }]
  },
  
  // 使用统计
  usage: {
    todayVoiceMinutes: { type: Number, default: 0 },      // 今日语音+画面使用分钟数
    monthVoiceMinutes: { type: Number, default: 0 },      // 本月语音+画面使用分钟数
    totalVoiceMinutes: { type: Number, default: 0 },      // 总计语音+画面使用分钟数
    lastResetDate: { type: Date, default: Date.now },

    // 多会话时间扣除统计
    multiSessionDeductions: { type: Number, default: 0 },  // 多会话扣除总时间
    maxConcurrentSessions: { type: Number, default: 0 },   // 历史最大并发会话数
    lastSessionCount: { type: Number, default: 0 },        // 上次会话数量
    lastDeductionTime: Date,                               // 上次扣除时间

    // 算力消耗统计
    totalComputationTasks: { type: Number, default: 0 },   // 总算力任务数
    activeComputationTasks: { type: Number, default: 0 },  // 当前活跃任务数
    totalComputationTime: { type: Number, default: 0 },    // 总算力消耗时间(毫秒)

    // 卡密使用统计
    totalMembershipTime: { type: Number, default: 0 },     // 总会员时间(毫秒)
    totalVoiceTime: { type: Number, default: 0 },          // 总语音时间(毫秒)
    totalVideoTime: { type: Number, default: 0 },          // 总视频时间(毫秒)
    cardKeysUsed: { type: Number, default: 0 },            // 已使用卡密数量
    lastCardKeyUsed: Date                                   // 最后使用卡密时间
  },
  
  // 个人资料
  profile: {
    avatar: String,
    bio: { type: String, maxlength: 500 },
    website: String,
    location: String,
    preferences: {
      language: { type: String, default: 'zh-CN' },
      theme: { type: String, enum: ['light', 'dark', 'auto'], default: 'auto' },
      notifications: {
        email: { type: Boolean, default: true },
        marketing: { type: Boolean, default: false }
      }
    }
  },
  
  // 安全信息
  security: {
    lastLoginAt: Date,
    lastLoginIP: String,
    loginAttempts: { type: Number, default: 0 },
    lockUntil: Date,
    passwordChangedAt: { type: Date, default: Date.now },
    twoFactorEnabled: { type: Boolean, default: false },
    twoFactorSecret: String
  },
  
  // 重置密码
  passwordResetToken: String,
  passwordResetExpires: Date,
  
  // 邮箱变更
  newEmail: String,
  emailChangeToken: String,
  emailChangeExpires: Date

}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
userSchema.index({ email: 1, role: 1 });
userSchema.index({ 'security.lastLoginAt': -1 });
userSchema.index({ 'usage.lastResetDate': 1 });
userSchema.index({ createdAt: -1 });

// 虚拟字段
userSchema.virtual('isLocked').get(function() {
  return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
});

userSchema.virtual('isEmailVerified').get(function() {
  return this.emailVerified;
});

// 角色权限映射
const rolePermissions = {
  user: ['voice_synthesis', 'voice_clone', 'batch_processing'], // 游客：每天30分钟语音+画面时长
  member: ['voice_synthesis', 'voice_clone', 'batch_processing'], // 会员：每天1小时语音+画面时长+会员标志
  agent: ['voice_synthesis', 'voice_clone', 'batch_processing', '代理控制'], // 代理人：每天2小时语音+画面时长+代理控制+代理标志
  admin: ['voice_synthesis', 'voice_clone', 'batch_processing', 'api_access', 'admin_panel', 'user_management'], // 管理员：∞语音+画面+会员时长+用户管理+公告管理
  administrator: ['*'] // 系统管理员：∞时长+所有权限包括系统设置
};

// 实例方法
userSchema.methods.hasPermission = function(permission) {
  if (this.role === 'administrator') return true;

  const rolePerms = rolePermissions[this.role] || [];
  return rolePerms.includes('*') || rolePerms.includes(permission) || this.permissions.includes(permission);
};

userSchema.methods.canUseVoice = function(minutes) {
  return this.usage.todayVoiceMinutes + minutes <= this.limits.dailyVoiceMinutes;
};

userSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return await bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.incrementLoginAttempts = function() {
  // 暂时禁用登录尝试限制
  // 如果之前有锁定且已过期，重置计数
  // if (this.security.lockUntil && this.security.lockUntil < Date.now()) {
  //   return this.updateOne({
  //     $unset: { 'security.lockUntil': 1 },
  //     $set: { 'security.loginAttempts': 1 }
  //   });
  // }

  // const updates = { $inc: { 'security.loginAttempts': 1 } };

  // 如果达到最大尝试次数且当前未锁定，则锁定账户
  // if (this.security.loginAttempts + 1 >= 5 && !this.isLocked) {
  //   updates.$set = { 'security.lockUntil': Date.now() + 2 * 60 * 60 * 1000 }; // 锁定2小时
  // }

  // return this.updateOne(updates);

  // 暂时只记录尝试次数，不进行锁定
  return this.updateOne({ $inc: { 'security.loginAttempts': 1 } });
};

userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { 'security.loginAttempts': 1, 'security.lockUntil': 1 }
  });
};

// 静态方法
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByUsername = function(username) {
  return this.findOne({ username: new RegExp(`^${username}$`, 'i') });
};

// 中间件
userSchema.pre('save', async function(next) {
  // 只有密码被修改时才进行哈希
  if (!this.isModified('password')) return next();
  
  try {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    this.password = await bcrypt.hash(this.password, saltRounds);
    this.security.passwordChangedAt = new Date();
    next();
  } catch (error) {
    next(error);
  }
});

userSchema.pre('save', function(next) {
  // 邮箱验证时间
  if (this.isModified('emailVerified') && this.emailVerified && !this.emailVerifiedAt) {
    this.emailVerifiedAt = new Date();
  }
  next();
});

// 删除敏感信息的方法
userSchema.methods.toSafeObject = function() {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.security.twoFactorSecret;
  delete userObject.passwordResetToken;
  delete userObject.emailChangeToken;
  return userObject;
};

module.exports = mongoose.model('User', userSchema);
