'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'GradeTwotone',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                opacity: '.3',
                d: 'M17.11 10.83l-2.47-.21l-1.2-.1l-.47-1.11L12 7.13l-.97 2.28l-.47 1.11l-1.2.1l-2.47.21l1.88 1.63l.91.79l-.27 1.17l-.57 2.42l2.13-1.28l1.03-.63l1.03.63l2.13 1.28l-.57-2.42l-.27-1.17l.91-.79z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            ),
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21L12 17.27L18.18 21l-1.64-7.03L22 9.24zm-7.41 5.18l.56 2.41l-2.12-1.28l-1.03-.62l-1.03.62l-2.12 1.28l.56-2.41l.27-1.18l-.91-.79l-1.88-1.63l2.47-.21l1.2-.1l.47-1.11l.97-2.27l.97 2.29l.47 1.11l1.2.1l2.47.21l-1.88 1.63l-.91.79l.27 1.16z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
