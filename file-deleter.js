const fs = require('fs');
const path = require('path');
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 颜色输出函数
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function colorLog(message, color = 'reset') {
    console.log(colors[color] + message + colors.reset);
}

// 强制删除文件函数
async function forceDeleteFile(filePath) {
    try {
        // 首先尝试移除只读属性
        try {
            const stats = fs.statSync(filePath);
            if (stats.isFile()) {
                fs.chmodSync(filePath, 0o666); // 给予读写权限
            }
        } catch (e) {
            // 忽略权限修改错误
        }

        // 删除文件
        fs.unlinkSync(filePath);
        colorLog(`✓ 成功删除文件: ${filePath}`, 'green');
        return true;
    } catch (error) {
        colorLog(`✗ 删除文件失败: ${filePath} - ${error.message}`, 'red');
        return false;
    }
}

// 强制删除文件夹函数
async function forceDeleteDirectory(dirPath) {
    try {
        // 递归删除文件夹内容
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const itemPath = path.join(dirPath, item);
            const stats = fs.statSync(itemPath);
            
            if (stats.isDirectory()) {
                await forceDeleteDirectory(itemPath);
            } else {
                await forceDeleteFile(itemPath);
            }
        }
        
        // 删除空文件夹
        fs.rmdirSync(dirPath);
        colorLog(`✓ 成功删除文件夹: ${dirPath}`, 'green');
        return true;
    } catch (error) {
        colorLog(`✗ 删除文件夹失败: ${dirPath} - ${error.message}`, 'red');
        return false;
    }
}

// 检查路径是否存在
function pathExists(targetPath) {
    try {
        fs.accessSync(targetPath);
        return true;
    } catch {
        return false;
    }
}

// 获取路径信息
function getPathInfo(targetPath) {
    try {
        const stats = fs.statSync(targetPath);
        return {
            exists: true,
            isFile: stats.isFile(),
            isDirectory: stats.isDirectory(),
            size: stats.size,
            modified: stats.mtime
        };
    } catch {
        return { exists: false };
    }
}

// 主删除函数
async function deletePath(targetPath) {
    const absolutePath = path.resolve(targetPath);
    const pathInfo = getPathInfo(absolutePath);
    
    if (!pathInfo.exists) {
        colorLog(`路径不存在: ${absolutePath}`, 'yellow');
        return false;
    }
    
    colorLog(`\n准备删除: ${absolutePath}`, 'blue');
    colorLog(`类型: ${pathInfo.isFile ? '文件' : '文件夹'}`, 'blue');
    
    if (pathInfo.isFile) {
        colorLog(`大小: ${(pathInfo.size / 1024).toFixed(2)} KB`, 'blue');
    }
    
    // 确认删除
    const answer = await new Promise(resolve => {
        rl.question('确认删除吗？(y/N): ', resolve);
    });
    
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
        colorLog('取消删除操作', 'yellow');
        return false;
    }
    
    // 执行删除
    colorLog('\n开始删除...', 'blue');
    
    if (pathInfo.isFile) {
        return await forceDeleteFile(absolutePath);
    } else {
        return await forceDeleteDirectory(absolutePath);
    }
}

// 批量删除函数
async function batchDelete(paths) {
    colorLog('\n=== 批量删除模式 ===', 'blue');
    let successCount = 0;
    let failCount = 0;
    
    for (const targetPath of paths) {
        const result = await deletePath(targetPath.trim());
        if (result) {
            successCount++;
        } else {
            failCount++;
        }
        colorLog(''); // 空行分隔
    }
    
    colorLog(`\n=== 删除完成 ===`, 'blue');
    colorLog(`成功: ${successCount} 个`, 'green');
    colorLog(`失败: ${failCount} 个`, 'red');
}

// 显示帮助信息
function showHelp() {
    colorLog('\n=== 文件删除工具使用说明 ===', 'blue');
    console.log('1. 单个删除: 直接输入文件或文件夹路径');
    console.log('2. 批量删除: 输入多个路径，用分号(;)分隔');
    console.log('3. 相对路径: 相对于当前目录');
    console.log('4. 绝对路径: 完整路径');
    console.log('5. 输入 "help" 显示帮助');
    console.log('6. 输入 "exit" 退出程序');
    colorLog('\n注意: 此工具会强制删除文件，请谨慎使用！', 'red');
}

// 主程序
async function main() {
    colorLog('=== 强制文件删除工具 ===', 'blue');
    colorLog('当前工作目录: ' + process.cwd(), 'blue');
    showHelp();
    
    while (true) {
        const input = await new Promise(resolve => {
            rl.question('\n请输入要删除的文件/文件夹路径 (或输入命令): ', resolve);
        });
        
        const trimmedInput = input.trim();
        
        if (trimmedInput === 'exit') {
            colorLog('再见！', 'green');
            break;
        }
        
        if (trimmedInput === 'help') {
            showHelp();
            continue;
        }
        
        if (trimmedInput === '') {
            colorLog('请输入有效路径', 'yellow');
            continue;
        }
        
        // 检查是否为批量删除（包含分号）
        if (trimmedInput.includes(';')) {
            const paths = trimmedInput.split(';').filter(p => p.trim());
            await batchDelete(paths);
        } else {
            await deletePath(trimmedInput);
        }
    }
    
    rl.close();
}

// 错误处理
process.on('uncaughtException', (error) => {
    colorLog(`程序错误: ${error.message}`, 'red');
    process.exit(1);
});

process.on('SIGINT', () => {
    colorLog('\n\n程序被中断', 'yellow');
    rl.close();
    process.exit(0);
});

// 启动程序
main().catch(error => {
    colorLog(`启动错误: ${error.message}`, 'red');
    process.exit(1);
});
