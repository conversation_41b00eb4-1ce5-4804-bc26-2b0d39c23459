'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'FreeCancellationRound',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M10.79 20H5V10h14v2.96c0 .89 1.08 1.34 1.71.71a.99.99 0 0 0 .29-.71V6c0-1.1-.9-2-2-2h-1V3c0-.55-.45-1-1-1s-1 .45-1 1v1H8V3c0-.55-.45-1-1-1s-1 .45-1 1v1H5c-1.11 0-1.99.9-1.99 2L3 20a2 2 0 0 0 2 2h5.79c.89 0 1.34-1.08.71-1.71c-.19-.18-.44-.29-.71-.29zm2.92-1.74a.996.996 0 0 1 1.41 0l1.41 1.41l3.54-3.54a.996.996 0 1 1 1.41 1.41l-4.24 4.24a.996.996 0 0 1-1.41 0l-2.12-2.12a.984.984 0 0 1 0-1.4zm-2.42-1.97a.996.996 0 0 1-1.41 0L9 15.41l-.88.88a.996.996 0 1 1-1.41-1.41l.88-.88l-.88-.88a.996.996 0 1 1 1.41-1.41l.88.88l.88-.88a.996.996 0 1 1 1.41 1.41l-.88.88l.88.88c.39.39.39 1.02 0 1.41z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
