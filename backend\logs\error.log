{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:06"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"SMTP连接验证失败: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","response":"535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","responseCode":535,"service":"voiceforge-api","stack":"Error: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023\n    at SMTPConnection._formatError (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-25 17:37:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:08"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:08"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:09"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:09"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:12"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:12"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:20"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:20"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:21"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:21"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:22"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:22"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:24"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:24"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:33"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:33"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:34"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:34"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:35"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:35"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:38"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:38"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:40"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:40"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:42"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:42"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:44"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:46"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:46"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:48"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:48"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:49"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:49"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:50"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:50"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:51"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:51"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:52"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:52"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:53"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:55"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"SMTP连接验证失败: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","response":"535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","responseCode":535,"service":"voiceforge-api","stack":"Error: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023\n    at SMTPConnection._formatError (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-25 17:37:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:57"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:57"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:58"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:58"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:59"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:37:59"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:00"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:00"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:02"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:02"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:03"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:03"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:04"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:04"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:05"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:05"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:08"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:10"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"SMTP连接验证失败: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","response":"535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","responseCode":535,"service":"voiceforge-api","stack":"Error: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023\n    at SMTPConnection._formatError (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-25 17:38:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:12"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:12"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:20"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:21"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:21"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:22"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:22"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:25"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"SMTP连接验证失败: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","response":"535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","responseCode":535,"service":"voiceforge-api","stack":"Error: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023\n    at SMTPConnection._formatError (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-25 17:38:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:33"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:33"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:34"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:34"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:35"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:35"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:38"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:38"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:40"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:40"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:42"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:42"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:44"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:44"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:46"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:46"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:48"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:48"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:49"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:49"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:50"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:51"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:51"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:52"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:52"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:53"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:53"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:54"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:54"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:57"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:57"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:58"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:58"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:59"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:38:59"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:00"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:00"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:02"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:02"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:03"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:03"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:04"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:04"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:05"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:05"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:08"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:08"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:09"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:09"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:12"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:12"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:20"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:20"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:21"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:21"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:22"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:22"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:24"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:24"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:33"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:34"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:34"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:35"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:35"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:38"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:38"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:40"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:40"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:42"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:42"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:44"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:44"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:46"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:46"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:48"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:48"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:49"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:49"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:50"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:50"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:51"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:51"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:52"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:52"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:53"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:53"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:54"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:54"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:57"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:58"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:58"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:59"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:39:59"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:00"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:00"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:01"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:02"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:02"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:03"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:03"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:04"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:04"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:05"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:05"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:06"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:07"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:08"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:08"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:09"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:09"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:10"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:11"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:12"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:12"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:13"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:14"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:15"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:16"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:17"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:18"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:19"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:20"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:20"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:21"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:22"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:22"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:23"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:24"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:24"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:25"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:26"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:27"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:28"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:29"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:30"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:31"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:32"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:33"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:33"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:34"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:36"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"SMTP连接验证失败: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","response":"535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","responseCode":535,"service":"voiceforge-api","stack":"Error: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023\n    at SMTPConnection._formatError (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-25 17:40:36"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:37"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:38"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:38"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:39"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:40"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:40"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:41"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:42"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:42"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:43"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:44"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:44"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:45"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:46"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:47"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:48"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:48"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:49"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:49"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:50"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:50"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:51"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:51"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:52"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:52"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:53"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:53"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:54"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:54"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:55"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:56"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:57"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:57"}
{"code":"ECONNREFUSED","level":"error","message":"Redis连接错误:","service":"voiceforge-api","stack":"AggregateError [ECONNREFUSED]: \n    at internalConnectMultiple (node:net:1134:18)\n    at afterConnectMultiple (node:net:1715:7)","timestamp":"2025-07-25 17:40:58"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"error","message":"SMTP连接验证失败: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","response":"535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023","responseCode":535,"service":"voiceforge-api","stack":"Error: Invalid login: 535 Login fail. Account is abnormal, service is not open, password is incorrect, login frequency limited, or system is busy. More information at https://help.mail.qq.com/detail/108/1023\n    at SMTPConnection._formatError (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at SMTPConnection._onSocketData (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\nodemailer@6.10.1\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-25 17:41:00"}
{"email":"<EMAIL>","error":"Redis连接不可用","level":"error","message":"发送验证码失败:","service":"voiceforge-api","stack":"Error: Redis连接不可用\n    at VerificationService.checkSendLimit (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\verificationService.js:119:13)\n    at VerificationService.sendVerificationCode (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\verificationService.js:208:37)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\auth.js:132:46\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-25 23:13:03","type":"register"}
{"email":"<EMAIL>","error":"Redis连接不可用","level":"error","message":"发送验证码失败:","service":"voiceforge-api","stack":"Error: Redis连接不可用\n    at VerificationService.checkSendLimit (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\verificationService.js:119:13)\n    at VerificationService.sendVerificationCode (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\verificationService.js:208:37)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\auth.js:132:46\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-25 23:14:07","type":"register"}
{"email":"<EMAIL>","error":"Redis连接不可用","level":"error","message":"发送验证码失败:","service":"voiceforge-api","stack":"Error: Redis连接不可用\n    at VerificationService.recordSendAttempt (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\verificationService.js:196:13)\n    at VerificationService.sendVerificationCode (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\verificationService.js:269:18)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\auth.js:132:20","timestamp":"2025-07-25 23:18:34","type":"register"}
{"ip":"::1","level":"error","message":"API错误: E11000 duplicate key error collection: voiceforge.users index: email_1 dup key: { email: \"<EMAIL>\" }","method":"POST","service":"voiceforge-api","stack":"MongoServerError: E11000 duplicate key error collection: voiceforge.users index: email_1 dup key: { email: \"<EMAIL>\" }\n    at InsertOneOperation.execute (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongodb@6.17.0\\node_modules\\mongodb\\lib\\operations\\insert.js:51:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async tryOperation (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongodb@6.17.0\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:207:20)\n    at async executeOperation (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongodb@6.17.0\\node_modules\\mongodb\\lib\\operations\\execute_operation.js:75:16)\n    at async Collection.insertOne (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongodb@6.17.0\\node_modules\\mongodb\\lib\\collection.js:157:16)","timestamp":"2025-07-26 14:59:58","url":"/api/auth/register","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: wechatId: 微信账号不能为空","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: wechatId: 微信账号不能为空\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3354:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3115:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-26 16:49:16","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: wechatId: 微信账号不能为空","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: wechatId: 微信账号不能为空\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3354:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3115:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-26 16:58:39","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: wechatId: 微信账号不能为空","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: wechatId: 微信账号不能为空\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3354:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3115:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-26 16:58:59","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: wechatId: 微信账号不能为空","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: wechatId: 微信账号不能为空\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3354:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3115:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-26 17:07:08","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: wechatId: 微信账号不能为空","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: wechatId: 微信账号不能为空\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3354:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3115:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-26 17:07:18","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: wechatId: 微信账号不能为空","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: wechatId: 微信账号不能为空\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3354:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3115:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 13:23:51","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: wechatId: 微信账号不能为空","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: wechatId: 微信账号不能为空\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3354:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\document.js:3115:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.4\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 13:23:52","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: logger.info is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: logger.info is not a function\n    at SessionManager.createSession (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\sessionManager.js:69:12)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\auth.js:504:40\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:18:33","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:82:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:128:3)","timestamp":"2025-07-28 16:09:12","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:82:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:128:3)","timestamp":"2025-07-28 16:14:17","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:82:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:128:3)","timestamp":"2025-07-28 16:14:31","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"startServerWithPortRetry is not defined","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"ReferenceError: startServerWithPortRetry is not defined\n    at startServer (D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js:132:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 16:48:21"}
{"error":"startServerWithPortRetry is not defined","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"ReferenceError: startServerWithPortRetry is not defined\n    at startServer (D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js:132:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 16:48:21"}
{"error":"端口 3004 被占用","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: 端口 3004 被占用\n    at Server.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js:122:20)\n    at Server.emit (node:events:518:28)\n    at emitErrorNT (node:net:1976:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-28 16:48:34"}
{"error":"端口 3004 被占用","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: 端口 3004 被占用\n    at Server.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js:122:20)\n    at Server.emit (node:events:518:28)\n    at emitErrorNT (node:net:1976:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-28 16:49:23"}
{"error":"端口 3004 被占用","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: 端口 3004 被占用\n    at Server.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js:122:20)\n    at Server.emit (node:events:518:28)\n    at emitErrorNT (node:net:1976:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-28 16:52:01"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: membershipLevel: `free` is not a valid enum value for path `membershipLevel`.","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: membershipLevel: `free` is not a valid enum value for path `membershipLevel`.\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 23:03:18","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: role: `agent` is not a valid enum value for path `role`.","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: role: `agent` is not a valid enum value for path `role`.\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 23:03:50","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: role: `agent` is not a valid enum value for path `role`.","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: role: `agent` is not a valid enum value for path `role`.\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 23:39:09","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: role: `agent` is not a valid enum value for path `role`.","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: role: `agent` is not a valid enum value for path `role`.\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 23:40:15","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: role: `agent` is not a valid enum value for path `role`.","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: role: `agent` is not a valid enum value for path `role`.\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 23:40:16","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: role: `agent` is not a valid enum value for path `role`.","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: role: `agent` is not a valid enum value for path `role`.\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 23:40:16","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: User validation failed: role: `agent` is not a valid enum value for path `role`.","method":"POST","service":"voiceforge-api","stack":"ValidationError: User validation failed: role: `agent` is not a valid enum value for path `role`.\n    at Document.invalidate (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3358:32)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\document.js:3119:17\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1410:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)","timestamp":"2025-07-28 23:40:16","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 01:02:10","url":"/users","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 01:02:29","url":"/users","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 01:03:26","url":"/users","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 01:03:29","url":"/users","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 01:03:33","url":"/users","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 01:50:07","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 01:50:07","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"PUT","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-07-29 02:00:50","url":"/users/1/permissions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"PUT","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-07-29 02:16:35","url":"/users/6886d82596e6e19a7186985c/permissions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:16:53","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 02:24:00","url":"/api/agent/cards?page=1&limit=20&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:37:37","url":"/cards?page=1&limit=12&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:38:01","url":"/cards?page=1&limit=12&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:38:31","url":"/cards?page=1&limit=12&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:39:25","url":"/quota","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:39:25","url":"/cards?page=1&limit=12&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:39:43","url":"/cards?page=1&limit=12&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:39:43","url":"/quota","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:44:38","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 02:44:38","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 02:46:19","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 02:46:19","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 03:04:07","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 03:04:07","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:09","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:09","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:11","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:11","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:13","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:13","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:33","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:33","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 03:39:53","url":"/api/agent/cards","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 10:00:37","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 10:00:42","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 10:00:42","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:14:30"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:14:51"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:15:08"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:20:38"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:20:51"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:21:02"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:21:14"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:21:29"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:21:41"}
{"error":"Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'express-validator'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\cardKeys.js:6:50)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-29 10:51:44"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:05:31","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:05:31","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:05:36","url":"/api/agent/cards","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:08:48","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:08:48","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:09:24","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:09:24","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:10:57","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:10:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:10:57","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"crypto.randomBytes is not a function","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"TypeError: crypto.randomBytes is not a function\n    at initTestData (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:287:27)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:317:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-07-29 11:11:23"}
{"error":"crypto.randomBytes is not a function","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"TypeError: crypto.randomBytes is not a function\n    at initTestData (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:304:27)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:334:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-07-29 11:12:28"}
{"error":"crypto.randomBytes is not a function","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"TypeError: crypto.randomBytes is not a function\n    at initTestData (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:304:27)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:334:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-07-29 11:17:25"}
{"error":"crypto.randomBytes is not a function","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"TypeError: crypto.randomBytes is not a function\n    at initTestData (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:304:27)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:334:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-07-29 11:44:14"}
{"error":"crypto.randomBytes is not a function","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"TypeError: crypto.randomBytes is not a function\n    at initTestData (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:304:27)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:334:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-07-29 11:44:29"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:45:58","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:45:58","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:47:41","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:47:41","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:47:56","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:47:56","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:49:38","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:49:38","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:51:20","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:51:20","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:53:47","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:53:47","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:54:00","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:54:00","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:54:13","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:54:13","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:54:30","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 11:54:30","url":"/api/agent/quota","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"POST","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 12:04:01","url":"/generate","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"POST","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 12:04:06","url":"/cards","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 12:05:23","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 12:05:23","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 12:10:23","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 12:10:36","url":"/api/agent/cards","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 12:10:40","url":"/api/agent/cards","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 12:10:52","url":"/api/agent/cards","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 12:34:20","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"GET","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 12:34:24","url":"/api/agent/cards?page=1&limit=12&status=&type=","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: req.user.hasPermission is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: req.user.hasPermission is not a function\n    at requireAgentPermission (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:13:17)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:145:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 12:34:26","url":"/api/agent/cards","user":"6886d82596e6e19a7186985c","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 12:55:25","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 12:55:25","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"cardIdCounter is not defined","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"ReferenceError: cardIdCounter is not defined\n    at initTestData (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:314:24)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:345:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-07-29 12:58:02"}
{"error":"agentQuotas is not defined","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"ReferenceError: agentQuotas is not defined\n    at initTestData (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:321:3)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:357:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-07-29 12:58:28"}
{"level":"error","message":"代理人生成卡密失败: agentQuotas is not defined","service":"voiceforge-api","stack":"ReferenceError: agentQuotas is not defined\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:157:28\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-validator@7.2.1\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 13:05:34"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"POST","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:15:09","url":"/cards","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:15:15","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:15:15","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"kind":"ObjectId","level":"error","message":"删除卡密失败: Cast to ObjectId failed for value \"undefined\" (type string) at path \"_id\" for model \"CardKey\"","path":"_id","reason":{},"service":"voiceforge-api","stack":"CastError: Cast to ObjectId failed for value \"undefined\" (type string) at path \"_id\" for model \"CardKey\"\n    at SchemaObjectId.cast (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\schema\\objectId.js:251:11)\n    at SchemaType.applySetters (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\schemaType.js:1258:12)\n    at SchemaType.castForQuery (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\schemaType.js:1676:17)\n    at cast (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\cast.js:390:32)\n    at Query.cast (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\query.js:5055:12)\n    at Query._castConditions (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\query.js:2351:10)\n    at model.Query._findOne (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\query.js:2674:8)\n    at model.Query.exec (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:267:18","stringValue":"\"undefined\"","timestamp":"2025-07-29 13:15:35","value":"undefined","valueType":"string"}
{"kind":"ObjectId","level":"error","message":"删除卡密失败: Cast to ObjectId failed for value \"undefined\" (type string) at path \"_id\" for model \"CardKey\"","path":"_id","reason":{},"service":"voiceforge-api","stack":"CastError: Cast to ObjectId failed for value \"undefined\" (type string) at path \"_id\" for model \"CardKey\"\n    at SchemaObjectId.cast (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\schema\\objectId.js:251:11)\n    at SchemaType.applySetters (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\schemaType.js:1258:12)\n    at SchemaType.castForQuery (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\schemaType.js:1676:17)\n    at cast (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\cast.js:390:32)\n    at Query.cast (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\query.js:5055:12)\n    at Query._castConditions (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\query.js:2351:10)\n    at model.Query._findOne (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\query.js:2674:8)\n    at model.Query.exec (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\mongoose@8.16.5\\node_modules\\mongoose\\lib\\query.js:4604:80)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:267:18","stringValue":"\"undefined\"","timestamp":"2025-07-29 13:15:38","value":"undefined","valueType":"string"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:32:18","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:32:18","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"error","message":"代理人生成卡密失败: 无效的卡密类型","service":"voiceforge-api","stack":"Error: 无效的卡密类型\n    at CardKeyManager.generateCardKeys (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\cardKeyManager.js:41:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:167:43\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-validator@7.2.1\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 13:37:11"}
{"level":"error","message":"代理人生成卡密失败: 无效的卡密类型","service":"voiceforge-api","stack":"Error: 无效的卡密类型\n    at CardKeyManager.generateCardKeys (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\cardKeyManager.js:41:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:167:43\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-validator@7.2.1\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 13:37:25"}
{"level":"error","message":"代理人生成卡密失败: 无效的卡密类型","service":"voiceforge-api","stack":"Error: 无效的卡密类型\n    at CardKeyManager.generateCardKeys (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\cardKeyManager.js:41:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:167:43\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-validator@7.2.1\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 13:37:30"}
{"level":"error","message":"代理人生成卡密失败: 无效的卡密类型","service":"voiceforge-api","stack":"Error: 无效的卡密类型\n    at CardKeyManager.generateCardKeys (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\cardKeyManager.js:41:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\agent.js:167:43\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-validator@7.2.1\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 13:37:36"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:49:11","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:49:11","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:49:26","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:49:26","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:49:51","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:49:51","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:50:06","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:50:06","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:50:23","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:50:23","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:50:38","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:50:38","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:51:51","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:51:51","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:52:26","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:52:26","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:52:40","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:52:40","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:53:03","url":"/cards?page=1&limit=1000&status=&type=","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:53:03","url":"/quota-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:58:36","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 13:58:36","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"POST","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 14:16:09","url":"/cards","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 14:57:41","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 16:05:59","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 16:05:59","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-29 17:14:07","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-29 21:56:35","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-29 21:56:36","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-29 21:56:38","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-29 21:56:42","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 09:29:20","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 10:11:14","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-30 10:38:58","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-30 10:38:59","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-30 10:39:13","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-30 10:39:14","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-30 10:39:16","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: 不允许的CORS源","method":"POST","service":"voiceforge-api","stack":"Error: 不允许的CORS源\n    at origin (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:86:18)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:219:13\n    at optionsCallback (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:199:9)\n    at corsMiddleware (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\cors@2.8.5\\node_modules\\cors\\lib\\index.js:204:7)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at customSecurityHeaders (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\security.js:132:3)","timestamp":"2025-07-30 10:39:20","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 10:40:22","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 10:59:56","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 10:59:56","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 11:17:01","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 11:17:01","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:60:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 11:43:40"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:60:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 11:49:44"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:60:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 11:51:18"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:60:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 12:03:20"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:60:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 12:04:13"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:60:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 12:04:25"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:60:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 12:13:05"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:62:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 12:13:46"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:62:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 12:14:00"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\fileTransfer.js:62:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 12:20:26"}
{"error":"Route.post() requires a callback function but got a [object Object]","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Route.post() requires a callback function but got a [object Object]\n    at Route.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:216:15)\n    at proto.<computed> [as post] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:521:19)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\routes\\resumableUpload.js:46:8)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)","timestamp":"2025-07-30 12:21:33"}
{"error":"Cannot find module 'ws'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\signalingServer.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'ws'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\signalingServer.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\signalingServer.js:1:19)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-30 12:44:38"}
{"error":"Cannot find module 'ws'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\signalingServer.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js","level":"error","message":"服务器启动失败","service":"voiceforge-api","stack":"Error: Cannot find module 'ws'\nRequire stack:\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\signalingServer.js\n- D:\\新建文件夹\\voiceforge-frontend\\backend\\server.js\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)\n    at Function._load (node:internal/modules/cjs/loader:1211:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (D:\\新建文件夹\\voiceforge-frontend\\backend\\services\\signalingServer.js:1:19)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)","timestamp":"2025-07-30 12:50:31"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 13:27:06","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 13:27:06","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 13:44:08","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 14:18:42","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 14:18:42","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 14:43:20","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Token无效或已过期","hasAuthHeader":true,"ip":"::1","level":"error","message":"认证中间件错误","method":"GET","service":"voiceforge-api","stack":"Error: Token无效或已过期\n    at verifyToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:22:11)\n    at authenticateToken (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\auth.js:54:21)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express@4.21.2\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-30 14:43:20","url":"/me","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"API错误: logger.warn is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: logger.warn is not a function\n    at Object.handler (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\rateLimiter.js:57:14)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-rate-limit@7.5.1_express@4.21.2\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-rate-limit@7.5.1_express@4.21.2\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-30 14:44:12","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.7.457"}
{"ip":"::1","level":"error","message":"API错误: logger.warn is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: logger.warn is not a function\n    at Object.handler (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\rateLimiter.js:57:14)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-rate-limit@7.5.1_express@4.21.2\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-rate-limit@7.5.1_express@4.21.2\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-30 14:44:13","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.7.457"}
{"ip":"::1","level":"error","message":"API错误: logger.warn is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: logger.warn is not a function\n    at Object.handler (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\rateLimiter.js:57:14)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-rate-limit@7.5.1_express@4.21.2\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-rate-limit@7.5.1_express@4.21.2\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-30 14:44:15","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.7.457"}
{"ip":"::1","level":"error","message":"API错误: logger.warn is not a function","method":"POST","service":"voiceforge-api","stack":"TypeError: logger.warn is not a function\n    at Object.handler (D:\\新建文件夹\\voiceforge-frontend\\backend\\middleware\\rateLimiter.js:57:14)\n    at D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-rate-limit@7.5.1_express@4.21.2\\node_modules\\express-rate-limit\\dist\\index.cjs:804:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\新建文件夹\\voiceforge-frontend\\backend\\node_modules\\.pnpm\\express-rate-limit@7.5.1_express@4.21.2\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-30 14:44:19","url":"/api/auth/login","user":"anonymous","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.2.7.457"}
