const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { logger, logSecurityEvent } = require('../utils/logger');

// 生成JWT token
const generateToken = (payload) => {
  return jwt.sign(
    payload,
    process.env.JWT_SECRET,
    { 
      expiresIn: process.env.JWT_EXPIRES_IN || '7d',
      issuer: process.env.APP_NAME || 'VoiceForge'
    }
  );
};

// 验证JWT token
const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    throw new Error('Token无效或已过期');
  }
};

// 认证中间件
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    // 详细日志：请求信息
    logger.debug('认证中间件 - 请求信息', {
      method: req.method,
      url: req.url,
      hasAuthHeader: !!authHeader,
      hasToken: !!token,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    if (!token) {
      logger.warn('认证失败 - 缺少token', {
        method: req.method,
        url: req.url,
        ip: req.ip
      });
      return res.status(401).json({
        success: false,
        message: '访问被拒绝，需要认证token'
      });
    }

    const decoded = verifyToken(token);
    logger.debug('Token解码成功', {
      userId: decoded.userId,
      sessionId: decoded.sessionId,
      iat: decoded.iat,
      exp: decoded.exp
    });

    // 验证用户是否存在且状态正常
    const user = await User.findById(decoded.userId).select('-password');
    if (!user) {
      logger.error('认证失败 - 用户不存在', {
        decodedUserId: decoded.userId,
        method: req.method,
        url: req.url,
        ip: req.ip
      });
      logSecurityEvent('invalid_user_token', { userId: decoded.userId }, req);
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (user.status !== 'active') {
      logger.warn('认证失败 - 用户状态异常', {
        userId: user._id,
        username: user.username,
        status: user.status,
        method: req.method,
        url: req.url
      });
      logSecurityEvent('inactive_user_access', {
        userId: user._id,
        status: user.status
      }, req);
      return res.status(401).json({
        success: false,
        message: '账户已被禁用'
      });
    }

    // 检查账户是否被锁定
    if (user.isLocked) {
      logger.warn('认证失败 - 账户被锁定', {
        userId: user._id,
        username: user.username,
        lockUntil: user.security.lockUntil,
        method: req.method,
        url: req.url
      });
      logSecurityEvent('locked_user_access', { userId: user._id }, req);
      return res.status(401).json({
        success: false,
        message: '账户已被锁定，请稍后再试'
      });
    }

    // 检查token是否在密码修改之后签发
    if (user.security.passwordChangedAt &&
        decoded.iat < Math.floor(user.security.passwordChangedAt.getTime() / 1000)) {
      logger.warn('认证失败 - Token在密码修改前签发', {
        userId: user._id,
        username: user.username,
        passwordChangedAt: user.security.passwordChangedAt,
        tokenIssuedAt: new Date(decoded.iat * 1000),
        method: req.method,
        url: req.url
      });
      logSecurityEvent('token_after_password_change', { userId: user._id }, req);
      return res.status(401).json({
        success: false,
        message: '密码已更改，请重新登录'
      });
    }

    // 添加会话ID到用户对象中，保持Mongoose实例方法
    req.user = user;
    req.user.sessionId = decoded.sessionId; // 从JWT中获取会话ID

    logger.debug('认证成功', {
      userId: user._id,
      username: user.username,
      role: user.role,
      method: req.method,
      url: req.url,
      sessionId: decoded.sessionId
    });

    next();
  } catch (error) {
    logger.error('认证中间件错误', {
      error: error.message,
      stack: error.stack,
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      hasAuthHeader: !!req.headers['authorization']
    });

    logSecurityEvent('token_verification_failed', {
      error: error.message,
      method: req.method,
      url: req.url
    }, req);

    return res.status(403).json({
      success: false,
      message: error.message
    });
  }
};

// 可选认证中间件（用户可能登录也可能未登录）
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token) {
      const decoded = verifyToken(token);
      const user = await User.findById(decoded.userId).select('-password');
      
      if (user && user.status === 'active' && !user.isLocked) {
        req.user = user;
      }
    }
    
    next();
  } catch (error) {
    // 忽略错误，继续执行
    next();
  }
};

// 权限检查中间件
const checkPermission = (requiredPermission) => {
  return (req, res, next) => {
    if (!req.user) {
      logger.warn('权限检查失败 - 用户未登录', {
        requiredPermission,
        method: req.method,
        url: req.url,
        ip: req.ip
      });
      return res.status(401).json({
        success: false,
        message: '需要登录'
      });
    }

    const hasPermission = req.user.hasPermission(requiredPermission);

    logger.debug('权限检查', {
      userId: req.user._id,
      username: req.user.username,
      requiredPermission,
      userRole: req.user.role,
      userPermissions: req.user.permissions,
      hasPermission,
      method: req.method,
      url: req.url
    });

    if (!hasPermission) {
      logger.warn('权限检查失败 - 权限不足', {
        userId: req.user._id,
        username: req.user.username,
        requiredPermission,
        userRole: req.user.role,
        userPermissions: req.user.permissions,
        method: req.method,
        url: req.url
      });

      logSecurityEvent('permission_denied', {
        userId: req.user._id,
        requiredPermission,
        userRole: req.user.role,
        userPermissions: req.user.permissions
      }, req);

      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    logger.debug('权限检查通过', {
      userId: req.user._id,
      username: req.user.username,
      requiredPermission
    });

    next();
  };
};

// 角色检查中间件
const checkRole = (requiredRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: '需要登录' 
      });
    }
    
    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    
    if (!roles.includes(req.user.role)) {
      logSecurityEvent('role_access_denied', { 
        userId: req.user._id,
        requiredRoles,
        userRole: req.user.role
      }, req);
      
      return res.status(403).json({ 
        success: false, 
        message: '角色权限不足' 
      });
    }
    
    next();
  };
};

// 使用限制检查中间件
const checkUsageLimit = (type, amount = 1) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: '需要登录' 
      });
    }
    
    try {
      switch (type) {
        case 'voice_minutes':
          if (!req.user.canUseVoice(amount)) {
            return res.status(429).json({ 
              success: false, 
              message: '今日语音时长已用完，请升级会员或明日再试',
              currentUsage: req.user.usage.todayVoiceMinutes,
              limit: req.user.limits.dailyVoiceMinutes
            });
          }
          break;
          
        case 'file_size':
          if (req.file && req.file.size > req.user.limits.maxFileSize * 1024 * 1024) {
            return res.status(413).json({ 
              success: false, 
              message: `文件大小超过限制(${req.user.limits.maxFileSize}MB)`,
              fileSize: Math.round(req.file.size / 1024 / 1024 * 100) / 100,
              limit: req.user.limits.maxFileSize
            });
          }
          break;
          
        default:
          break;
      }
      
      next();
    } catch (error) {
      logger.error('使用限制检查失败:', error);
      return res.status(500).json({ 
        success: false, 
        message: '服务器内部错误' 
      });
    }
  };
};

// 刷新token
const refreshToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET, { ignoreExpiration: true });
    
    // 检查token是否在可刷新时间内（比如过期后24小时内）
    const now = Math.floor(Date.now() / 1000);
    const maxRefreshTime = decoded.exp + (24 * 60 * 60); // 24小时
    
    if (now > maxRefreshTime) {
      throw new Error('Token已过期，无法刷新');
    }
    
    // 生成新token
    const newPayload = {
      userId: decoded.userId,
      email: decoded.email
    };
    
    return generateToken(newPayload);
  } catch (error) {
    throw new Error('Token刷新失败');
  }
};

module.exports = {
  generateToken,
  verifyToken,
  authenticateToken,
  optionalAuth,
  checkPermission,
  checkRole,
  checkUsageLimit,
  refreshToken
};
