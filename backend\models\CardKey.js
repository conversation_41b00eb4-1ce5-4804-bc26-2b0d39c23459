const mongoose = require('mongoose');

/**
 * 卡密模型
 * 支持会员卡密、语音卡密、视频卡密三种类型
 */
const cardKeySchema = new mongoose.Schema({
  // 卡密基本信息
  cardKey: {
    type: String,
    required: [true, '卡密不能为空'],
    unique: true,
    uppercase: true,
    match: [/^[A-Z]{3}[0-9A-Z]-[0-9A-Z]{4}-[0-9A-Z]{4}-[0-9A-Z]{4}-[0-9A-Z]{4}$/, '卡密格式不正确'],
    index: true
  },

  // 卡密类型
  type: {
    type: String,
    enum: ['member', 'member-year', 'voice', 'voice-year', 'video', 'video-year'],
    required: [true, '卡密类型不能为空'],
    index: true
  },

  // 卡密状态
  status: {
    type: String,
    enum: ['unused', 'used', 'expired', 'disabled'],
    default: 'unused',
    index: true
  },

  // 卡密价值信息
  value: {
    // 时长（分钟）
    duration: {
      type: Number,
      required: [true, '时长不能为空'],
      min: [1, '时长必须大于0']
    },
    
    // 时长单位
    unit: {
      type: String,
      enum: ['minutes', 'hours', 'days', 'months'],
      default: 'days'
    },
    
    // 描述
    description: {
      type: String,
      maxlength: [200, '描述不能超过200个字符']
    }
  },

  // 使用信息
  usage: {
    // 使用者ID
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      index: true
    },
    
    // 使用时间
    usedAt: {
      type: Date,
      index: true
    },
    
    // 使用者IP
    usedIp: {
      type: String
    },
    
    // 使用设备信息
    deviceInfo: {
      userAgent: String,
      platform: String
    }
  },

  // 生成信息
  generation: {
    // 生成者ID（管理员或代理人）
    generatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '生成者不能为空'],
      index: true
    },
    
    // 生成时间
    generatedAt: {
      type: Date,
      default: Date.now,
      index: true
    },
    
    // 生成批次ID
    batchId: {
      type: String,
      index: true
    },
    
    // 生成备注
    note: {
      type: String,
      maxlength: [500, '备注不能超过500个字符']
    }
  },

  // 有效期设置
  validity: {
    // 生效时间（卡密生成时间）
    validFrom: {
      type: Date,
      default: Date.now
    },

    // 过期时间（卡密本身的过期时间，不是时长过期时间）
    validUntil: {
      type: Date,
      index: true
    },

    // 是否永不过期
    neverExpires: {
      type: Boolean,
      default: false
    }
  },

  // 激活和计时设置
  activation: {
    // 是否已激活（只有激活后才开始计时）
    isActivated: {
      type: Boolean,
      default: false,
      index: true
    },

    // 激活时间（开始计时的时间）
    activatedAt: {
      type: Date,
      index: true
    },

    // 时长结束时间（激活时间 + 卡密时长）
    timeEndDate: {
      type: Date,
      index: true
    }
  },

  // 限制条件
  restrictions: {
    // 使用次数限制
    maxUses: {
      type: Number,
      default: 1,
      min: [1, '使用次数必须大于0']
    },
    
    // 已使用次数
    usedCount: {
      type: Number,
      default: 0,
      min: 0
    },
    
    // IP限制
    allowedIps: [{
      type: String
    }],
    
    // 用户角色限制
    allowedRoles: [{
      type: String,
      enum: ['user', 'member', 'agent', 'admin', 'administrator']
    }]
  },

  // 统计信息
  stats: {
    // 查询次数
    queryCount: {
      type: Number,
      default: 0
    },
    
    // 最后查询时间
    lastQueried: {
      type: Date
    },
    
    // 查询IP记录
    queryIps: [{
      ip: String,
      timestamp: { type: Date, default: Date.now },
      userAgent: String
    }]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 虚拟字段：是否已过期
cardKeySchema.virtual('isExpired').get(function() {
  if (this.validity.neverExpires) return false;
  if (!this.validity.validUntil) return false;
  return new Date() > this.validity.validUntil;
});

// 虚拟字段：是否可用
cardKeySchema.virtual('isAvailable').get(function() {
  return this.status === 'unused' && 
         !this.isExpired && 
         this.restrictions.usedCount < this.restrictions.maxUses;
});

// 虚拟字段：剩余使用次数
cardKeySchema.virtual('remainingUses').get(function() {
  return Math.max(0, this.restrictions.maxUses - this.restrictions.usedCount);
});

// 虚拟字段：卡密类型信息
cardKeySchema.virtual('typeInfo').get(function() {
  const typeMap = {
    'member': { name: '会员卡密', prefix: 'VIP', color: '#FFD700', icon: '👑' },
    'voice': { name: '语音卡密', prefix: 'VOC', color: '#4CAF50', icon: '🎤' },
    'video': { name: '视频卡密', prefix: 'VID', color: '#2196F3', icon: '🎬' }
  };
  return typeMap[this.type] || { name: '未知类型', prefix: 'UNK', color: '#999999', icon: '❓' };
});

// 索引优化
cardKeySchema.index({ type: 1, status: 1 });
cardKeySchema.index({ 'generation.generatedBy': 1, 'generation.generatedAt': -1 });
cardKeySchema.index({ 'usage.userId': 1, 'usage.usedAt': -1 });
cardKeySchema.index({ 'validity.validUntil': 1 });
cardKeySchema.index({ cardKey: 1 }, { unique: true });

// 中间件：保存前验证
cardKeySchema.pre('save', function(next) {
  // 验证卡密格式
  if (this.isNew || this.isModified('cardKey')) {
    const prefix = this.cardKey.substring(0, 3);
    const expectedPrefix = this.typeInfo.prefix;
    
    if (prefix !== expectedPrefix) {
      return next(new Error(`${this.typeInfo.name}必须以${expectedPrefix}开头`));
    }
  }
  
  // 自动设置过期状态
  if (this.isExpired && this.status === 'unused') {
    this.status = 'expired';
  }
  
  next();
});

// 静态方法：生成卡密
cardKeySchema.statics.generateCardKey = function(type) {
  const typeMap = {
    'member': 'VIP',
    'voice': 'VOC', 
    'video': 'VID'
  };
  
  const prefix = typeMap[type];
  if (!prefix) {
    throw new Error('无效的卡密类型');
  }
  
  // 生成校验码
  const checksum = Math.floor(Math.random() * 10);
  
  // 生成随机码（避免混淆字符）
  const chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ';
  const generateSegment = (length) => {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };
  
  // 生成时间标识（当前月份编码）
  const month = new Date().getMonth() + 1;
  const timeCode = month.toString().padStart(2, '0');
  
  // 组装卡密：PREFIX + CHECKSUM + 4段随机码 + 校验码 + 时间码
  const cardKey = `${prefix}${checksum}-${generateSegment(4)}-${generateSegment(4)}-${generateSegment(4)}-${generateSegment(2)}${timeCode}`;
  
  return cardKey;
};

// 实例方法：使用卡密
cardKeySchema.methods.redeem = async function(userId, ipAddress, deviceInfo) {
  if (!this.isAvailable) {
    throw new Error('卡密不可用');
  }
  
  // 更新使用信息
  this.status = 'used';
  this.usage.userId = userId;
  this.usage.usedAt = new Date();
  this.usage.usedIp = ipAddress;
  this.usage.deviceInfo = deviceInfo;
  this.restrictions.usedCount += 1;
  
  await this.save();
  
  return {
    type: this.type,
    value: this.value,
    cardKey: this.cardKey
  };
};

// 实例方法：记录查询
cardKeySchema.methods.recordQuery = async function(ipAddress, userAgent) {
  this.stats.queryCount += 1;
  this.stats.lastQueried = new Date();
  
  // 记录查询IP（最多保留100条）
  this.stats.queryIps.push({
    ip: ipAddress,
    userAgent: userAgent
  });
  
  if (this.stats.queryIps.length > 100) {
    this.stats.queryIps = this.stats.queryIps.slice(-100);
  }
  
  await this.save();
};

module.exports = mongoose.model('CardKey', cardKeySchema);
