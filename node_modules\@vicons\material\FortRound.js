'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'FortRound',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M21 4v1h-2V4c0-.55-.45-1-1-1s-1 .45-1 1v1h-2V4c0-.55-.45-1-1-1s-1 .45-1 1v2.17c0 .53.21 1.04.59 1.41L15 9v1H9V9l1.41-1.41c.38-.38.59-.89.59-1.42V4c0-.55-.45-1-1-1s-1 .45-1 1v1H7V4c0-.55-.45-1-1-1s-1 .45-1 1v1H3V4c0-.55-.45-1-1-1s-1 .45-1 1v2.17c0 .53.21 1.04.59 1.42L3 9v6l-1.41 1.41c-.38.38-.59.89-.59 1.42V19c0 1.1.9 2 2 2h7v-2.89c0-1 .68-1.92 1.66-2.08A2 2 0 0 1 14 18v3h7c1.1 0 2-.9 2-2v-1.17c0-.53-.21-1.04-.59-1.41L21 15V9l1.41-1.41c.38-.38.59-.89.59-1.42V4c0-.55-.45-1-1-1s-1 .45-1 1z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
