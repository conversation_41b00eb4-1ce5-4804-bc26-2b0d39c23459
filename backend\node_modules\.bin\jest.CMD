@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\新建文件夹\voiceforge-frontend\backend\node_modules\.pnpm\jest@29.7.0_@types+node@24.1.0\node_modules\jest\bin\node_modules;D:\新建文件夹\voiceforge-frontend\backend\node_modules\.pnpm\jest@29.7.0_@types+node@24.1.0\node_modules\jest\node_modules;D:\新建文件夹\voiceforge-frontend\backend\node_modules\.pnpm\jest@29.7.0_@types+node@24.1.0\node_modules;D:\新建文件夹\voiceforge-frontend\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\新建文件夹\voiceforge-frontend\backend\node_modules\.pnpm\jest@29.7.0_@types+node@24.1.0\node_modules\jest\bin\node_modules;D:\新建文件夹\voiceforge-frontend\backend\node_modules\.pnpm\jest@29.7.0_@types+node@24.1.0\node_modules\jest\node_modules;D:\新建文件夹\voiceforge-frontend\backend\node_modules\.pnpm\jest@29.7.0_@types+node@24.1.0\node_modules;D:\新建文件夹\voiceforge-frontend\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\jest@29.7.0_@types+node@24.1.0\node_modules\jest\bin\jest.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\jest@29.7.0_@types+node@24.1.0\node_modules\jest\bin\jest.js" %*
)
