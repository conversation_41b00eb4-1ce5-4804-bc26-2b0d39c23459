// Nuxt项目主样式文件
// 导入变量文件
@import './variables.scss';

// Nuxt特定样式
html {
  scroll-behavior: smooth;
}

// 页面过渡动画
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

// 布局过渡
.layout-enter-active,
.layout-leave-active {
  transition: all 0.3s ease;
}

.layout-enter-from,
.layout-leave-to {
  opacity: 0;
}

// 加载状态
.nuxt-loading-indicator {
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  height: 3px;
}

// 错误页面样式
.error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  text-align: center;
  
  h1 {
    font-size: 4rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: 1rem;
  }
  
  h2 {
    font-size: 1.5rem;
    color: var(--color-text);
    margin-bottom: 1rem;
  }
  
  p {
    color: var(--color-text-secondary);
    margin-bottom: 2rem;
  }
  
  .error-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
  }
}
