// Nuxt项目样式变量文件
// 移除循环引用，直接定义所需变量

// 基础颜色变量
:root {
  // 主色调
  --color-primary: #6366f1;
  --color-primary-dark: #4f46e5;
  --color-primary-light: #8b5cf6;

  // 文本颜色
  --color-text: #1f2937;
  --color-text-secondary: #6b7280;
  --color-text-light: #9ca3af;

  // 背景颜色
  --color-bg: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-dark: #111827;

  // 边框颜色
  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;
}

// Nuxt特定变量
:root {
  // 页面过渡时间
  --transition-page: 0.3s ease;
  --transition-layout: 0.3s ease;
  
  // Z-index层级
  --z-loading: 9999;
  --z-modal: 1000;
  --z-header: 100;
  --z-footer: 10;
}

// 响应式断点
$breakpoints: (
  'xs': 480px,
  'sm': 640px,
  'md': 768px,
  'lg': 1024px,
  'xl': 1280px,
  '2xl': 1536px
);

// 媒体查询混合器
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin respond-below($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}
