'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 24 24'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'GamesTwotone',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                opacity: '.3',
                d: 'M11 17.33V20h2v-2.67l-1-1zm2-10.66V4h-2v2.67l1 1zM16.33 12l1 1H20v-2h-2.67zM4 11v2h2.67l1-1l-1-1z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            ),
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M2 9v6h5.5l3-3l-3-3H2zm4.67 4H4v-2h2.67l1 1l-1 1zM22 9h-5.5l-3 3l3 3H22V9zm-2 4h-2.67l-1-1l1-1H20v2zm-5 3.5l-3-3l-3 3V22h6v-5.5zM13 20h-2v-2.67l1-1l1 1V20zM9 7.5l3 3l3-3V2H9v5.5zM11 4h2v2.67l-1 1l-1-1V4z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
