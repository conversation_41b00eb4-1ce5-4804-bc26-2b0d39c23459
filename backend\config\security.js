const crypto = require('crypto');

/**
 * 安全配置管理模块
 * 统一管理所有安全相关的配置和密钥
 */

// 验证必需的环境变量
const requiredEnvVars = [
  'JWT_SECRET',
  'JWT_REFRESH_SECRET',
  'MONGODB_URI'
];

function validateEnvironment() {
  const missing = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }
  
  // 验证JWT密钥长度
  if (process.env.JWT_SECRET.length < 32) {
    throw new Error('JWT_SECRET 长度必须至少32个字符');
  }
  
  if (process.env.JWT_REFRESH_SECRET.length < 32) {
    throw new Error('JWT_REFRESH_SECRET 长度必须至少32个字符');
  }
  
  // 确保两个密钥不同
  if (process.env.JWT_SECRET === process.env.JWT_REFRESH_SECRET) {
    throw new Error('JWT_SECRET 和 JWT_REFRESH_SECRET 必须不同');
  }
}

// 生成安全的随机密钥
function generateSecureKey(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

// JWT配置
const jwtConfig = {
  secret: process.env.JWT_SECRET,
  refreshSecret: process.env.JWT_REFRESH_SECRET,
  expiresIn: process.env.JWT_EXPIRES_IN || '15m',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  rememberExpiresIn: process.env.JWT_REMEMBER_EXPIRES_IN || '30d',
  issuer: process.env.APP_NAME || 'VoiceForge',
  audience: process.env.APP_URL || 'http://localhost:3000'
};

// 速率限制配置
const rateLimitConfig = {
  // 通用API限制
  api: {
    windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW) * 60 * 1000 || 60000, // 1分钟
    max: parseInt(process.env.API_RATE_LIMIT_MAX) || 1000,
    message: 'API调用过于频繁，请稍后再试'
  },
  
  // 登录限制
  login: {
    windowMs: parseInt(process.env.LOGIN_RATE_LIMIT_WINDOW) * 60 * 1000 || 300000, // 5分钟
    max: parseInt(process.env.LOGIN_RATE_LIMIT_MAX) || 5,
    message: '登录尝试过于频繁，请5分钟后再试',
    skipSuccessfulRequests: true
  },
  
  // 注册限制
  register: {
    windowMs: 60 * 60 * 1000, // 1小时
    max: 3,
    message: '注册尝试过于频繁，请1小时后再试'
  },
  
  // 密码重置限制
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1小时
    max: 3,
    message: '密码重置请求过于频繁，请1小时后再试'
  }
};

// 会话配置
const sessionConfig = {
  timeout: parseInt(process.env.SESSION_TIMEOUT) * 60 * 1000 || 900000, // 15分钟
  maxConcurrent: parseInt(process.env.MAX_CONCURRENT_SESSIONS) || 3,
  cleanupInterval: parseInt(process.env.SESSION_CLEANUP_INTERVAL) * 1000 || 300000, // 5分钟
  cookieName: 'voiceforge_session',
  cookieOptions: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: parseInt(process.env.SESSION_TIMEOUT) * 60 * 1000 || 900000
  }
};

// 安全头配置
const securityHeaders = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: 31536000, // 1年
    includeSubDomains: true,
    preload: true
  }
};

// CORS配置 - 固定前端端口3003，后端端口3000
console.log('Environment CORS_ORIGIN:', process.env.CORS_ORIGIN);
const corsConfig = {
  origin: [
    'http://localhost:3003', // 固定前端端口
    'http://localhost:3000'  // 允许同端口访问（如果需要）
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
  exposedHeaders: ['X-Total-Count', 'X-Rate-Limit-Remaining']
};

// 密码策略配置
const passwordPolicy = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
  maxRepeatingChars: 3,
  preventCommonPasswords: true
};

// 审计日志配置
const auditConfig = {
  enabled: true,
  logLevel: process.env.LOG_LEVEL || 'info',
  logFile: process.env.LOG_FILE || 'logs/audit.log',
  maxFileSize: '10MB',
  maxFiles: 5,
  events: {
    login: true,
    logout: true,
    register: true,
    passwordChange: true,
    passwordReset: true,
    profileUpdate: true,
    permissionChange: true,
    securityEvent: true
  }
};

// 初始化安全配置
function initializeSecurity() {
  try {
    validateEnvironment();
    console.log('✅ 安全配置验证通过');
    return true;
  } catch (error) {
    console.error('❌ 安全配置验证失败:', error.message);
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
    return false;
  }
}

module.exports = {
  initializeSecurity,
  generateSecureKey,
  jwtConfig,
  rateLimitConfig,
  sessionConfig,
  securityHeaders,
  corsConfig,
  passwordPolicy,
  auditConfig
};
