const mongoose = require('mongoose');

const announcementSchema = new mongoose.Schema({
  // 基本信息
  title: {
    type: String,
    required: [true, '公告标题不能为空'],
    maxlength: [200, '标题不能超过200个字符'],
    trim: true
  },
  
  content: {
    type: String,
    required: [true, '公告内容不能为空'],
    trim: true
  },
  
  summary: {
    type: String,
    maxlength: [500, '摘要不能超过500个字符'],
    trim: true
  },
  
  // 公告类型（对应现有标签）
  type: {
    type: String,
    enum: ['permanent', 'feature', 'maintenance', 'promotion', 'event', 'security'],
    required: [true, '公告类型不能为空']
  },
  
  // 图标
  icon: {
    type: String,
    default: '📢'
  },
  
  // 优先级
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // 状态
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  },
  
  // 显示控制
  isPinned: {
    type: Boolean,
    default: false,
    comment: '是否置顶显示'
  },
  
  isPermanent: {
    type: Boolean,
    default: false,
    comment: '是否为永不关闭的重要公告'
  },
  
  autoShow: {
    type: Boolean,
    default: true,
    comment: '是否自动弹出显示'
  },
  
  // 目标用户
  targetUsers: {
    type: String,
    enum: ['all', 'free', 'basic', 'premium', 'enterprise', 'admin'],
    default: 'all'
  },
  
  // 图片附件
  images: [{
    url: String,
    alt: String,
    caption: String
  }],
  
  // 时间控制
  publishAt: {
    type: Date,
    default: Date.now
  },
  
  expireAt: {
    type: Date,
    comment: '过期时间，过期后不再显示'
  },
  
  // 创建者信息
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 统计信息
  viewCount: {
    type: Number,
    default: 0
  }
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
announcementSchema.index({ status: 1, publishAt: -1 });
announcementSchema.index({ type: 1, status: 1 });
announcementSchema.index({ isPinned: -1, publishAt: -1 });
announcementSchema.index({ createdBy: 1, createdAt: -1 });

// 虚拟字段
announcementSchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === 'published' && 
         this.publishAt <= now && 
         (!this.expireAt || this.expireAt > now);
});

announcementSchema.virtual('typeName').get(function() {
  const typeMap = {
    'permanent': '重要',
    'feature': '功能',
    'maintenance': '维护',
    'promotion': '活动',
    'event': '事件',
    'security': '安全'
  };
  return typeMap[this.type] || '通知';
});

// 实例方法
announcementSchema.methods.incrementViewCount = function() {
  this.viewCount += 1;
  return this.save();
};

// 静态方法
announcementSchema.statics.getActiveAnnouncements = function(targetUser = 'all') {
  const now = new Date();
  const query = {
    status: 'published',
    publishAt: { $lte: now },
    $or: [
      { expireAt: { $exists: false } },
      { expireAt: { $gt: now } }
    ]
  };
  
  if (targetUser !== 'all') {
    query.$or = [
      { targetUsers: 'all' },
      { targetUsers: targetUser }
    ];
  }
  
  return this.find(query)
    .populate('createdBy', 'username')
    .sort({ isPinned: -1, publishAt: -1 });
};

announcementSchema.statics.getByType = function(type) {
  return this.find({ type, status: 'published' })
    .populate('createdBy', 'username')
    .sort({ isPinned: -1, publishAt: -1 });
};

// 中间件
announcementSchema.pre('save', function(next) {
  // 如果是永久公告，自动设置为置顶
  if (this.isPermanent) {
    this.isPinned = true;
  }
  
  // 如果没有摘要，自动从内容中提取
  if (!this.summary && this.content) {
    // 移除HTML标签并截取前200个字符
    const plainText = this.content.replace(/<[^>]*>/g, '');
    this.summary = plainText.substring(0, 200) + (plainText.length > 200 ? '...' : '');
  }
  
  next();
});

module.exports = mongoose.model('Announcement', announcementSchema);
