const rateLimit = require('express-rate-limit');
const MongoStore = require('rate-limit-mongo');
const { rateLimitConfig } = require('../config/security');
const logger = require('../utils/logger');

/**
 * 速率限制中间件
 * 防止暴力破解和API滥用
 */

// MongoDB存储配置（用于集群环境）
const mongoStore = new MongoStore({
  uri: process.env.MONGODB_URI,
  collectionName: 'rate_limits',
  expireTimeMs: 15 * 60 * 1000 // 15分钟过期
});

// 创建速率限制器
function createRateLimiter(config, options = {}) {
  return rateLimit({
    store: mongoStore,
    windowMs: config.windowMs,
    max: config.max,
    message: {
      success: false,
      message: config.message,
      retryAfter: Math.ceil(config.windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: config.skipSuccessfulRequests || false,
    
    // 自定义键生成器（基于IP和用户）
    keyGenerator: (req) => {
      const ip = req.ip || req.connection.remoteAddress;
      const userId = req.user?.id || 'anonymous';
      return `${ip}:${userId}:${options.prefix || 'default'}`;
    },
    
    // 跳过条件
    skip: (req) => {
      // 跳过本地开发环境
      if (process.env.NODE_ENV === 'development' && req.ip === '127.0.0.1') {
        return true;
      }
      
      // 跳过白名单IP
      const whitelist = process.env.RATE_LIMIT_WHITELIST?.split(',') || [];
      return whitelist.includes(req.ip);
    },
    
    // 达到限制时的处理 (使用新的 handler 方式)
    handler: (req, res, next, options) => {
      const ip = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent') || 'Unknown';

      logger.warn('速率限制触发', {
        ip,
        userAgent,
        endpoint: req.originalUrl,
        method: req.method,
        limit: config.max,
        window: config.windowMs / 1000,
        type: options.prefix || 'general'
      });

      // 记录安全事件
      if (options.securityEvent) {
        logger.security('rate_limit_exceeded', {
          ip,
          endpoint: req.originalUrl,
          method: req.method,
          userAgent,
          limit: config.max
        });
      }

      // 返回限制响应
      res.status(429).json({
        success: false,
        message: config.message,
        retryAfter: Math.ceil(config.windowMs / 1000)
      });
    },
    
    ...options
  });
}

// 通用API速率限制
const apiLimiter = createRateLimiter(rateLimitConfig.api, {
  prefix: 'api'
});

// 登录速率限制
const loginLimiter = createRateLimiter(rateLimitConfig.login, {
  prefix: 'login',
  securityEvent: true,
  keyGenerator: (req) => {
    // 基于IP和用户标识符
    const ip = req.ip || req.connection.remoteAddress;
    const identifier = req.body?.identifier || req.body?.email || 'unknown';
    return `login:${ip}:${identifier}`;
  }
});

// 注册速率限制
const registerLimiter = createRateLimiter(rateLimitConfig.register, {
  prefix: 'register',
  securityEvent: true
});

// 密码重置速率限制
const passwordResetLimiter = createRateLimiter(rateLimitConfig.passwordReset, {
  prefix: 'password_reset',
  securityEvent: true,
  keyGenerator: (req) => {
    const ip = req.ip || req.connection.remoteAddress;
    const email = req.body?.email || 'unknown';
    return `password_reset:${ip}:${email}`;
  }
});

// 验证码发送限制
const verificationCodeLimiter = rateLimit({
  store: mongoStore,
  windowMs: 60 * 1000, // 1分钟
  max: 1, // 每分钟最多1次
  message: {
    success: false,
    message: '验证码发送过于频繁，请1分钟后再试',
    retryAfter: 60
  },
  keyGenerator: (req) => {
    const ip = req.ip || req.connection.remoteAddress;
    const email = req.body?.email || 'unknown';
    return `verification:${ip}:${email}`;
  }
});

// 文件上传限制
const uploadLimiter = rateLimit({
  store: mongoStore,
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 每分钟最多10个文件
  message: {
    success: false,
    message: '文件上传过于频繁，请稍后再试',
    retryAfter: 60
  }
});

// 动态速率限制（基于用户等级）
function createDynamicLimiter(baseConfig) {
  return (req, res, next) => {
    const user = req.user;
    let config = { ...baseConfig };
    
    // 根据用户等级调整限制
    if (user) {
      switch (user.role) {
        case 'super_admin':
          config.max *= 10; // 管理员10倍限制
          break;
        case 'admin':
          config.max *= 5; // 管理员5倍限制
          break;
        case 'premium':
          config.max *= 3; // 高级用户3倍限制
          break;
        case 'basic':
          config.max *= 2; // 基础用户2倍限制
          break;
        default:
          // 免费用户使用默认限制
          break;
      }
    }
    
    const limiter = createRateLimiter(config, {
      prefix: 'dynamic'
    });
    
    return limiter(req, res, next);
  };
}

// IP黑名单检查
function ipBlacklistCheck(req, res, next) {
  const ip = req.ip || req.connection.remoteAddress;
  const blacklist = process.env.IP_BLACKLIST?.split(',') || [];
  
  if (blacklist.includes(ip)) {
    logger.security('blocked_ip_access', {
      ip,
      endpoint: req.originalUrl,
      method: req.method,
      userAgent: req.get('User-Agent')
    });
    
    return res.status(403).json({
      success: false,
      message: '访问被拒绝'
    });
  }
  
  next();
}

// 清理过期的速率限制记录
async function cleanupRateLimits() {
  try {
    const db = mongoStore.db;
    const collection = db.collection('rate_limits');
    
    // 删除过期记录
    const result = await collection.deleteMany({
      resetTime: { $lt: new Date() }
    });
    
    if (result.deletedCount > 0) {
      logger.info(`清理了 ${result.deletedCount} 条过期的速率限制记录`);
    }
  } catch (error) {
    logger.error('清理速率限制记录失败:', error);
  }
}

// 定期清理任务
setInterval(cleanupRateLimits, 60 * 60 * 1000); // 每小时清理一次

module.exports = {
  apiLimiter,
  loginLimiter,
  registerLimiter,
  passwordResetLimiter,
  verificationCodeLimiter,
  uploadLimiter,
  createDynamicLimiter,
  ipBlacklistCheck,
  cleanupRateLimits
};
