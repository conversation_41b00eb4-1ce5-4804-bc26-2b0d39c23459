{"version": 3, "names": ["_OverloadYield", "value", "kind", "v", "k"], "sources": ["../../src/helpers/OverloadYield.ts"], "sourcesContent": ["/* @minVersion 7.18.14 */\n\nconst enum Kind {\n  // This yield was an await expression\n  Await = 0,\n  // This yield comes from yield*\n  Delegate = 1,\n}\n\n// _OverloadYield is actually a class\ndeclare class _OverloadYield<T = any> {\n  constructor(value: T, /** 0: await 1: delegate */ kind: 0 | 1);\n\n  v: T;\n  k: Kind;\n}\n\n// The actual implementation of _OverloadYield starts here\nfunction _OverloadYield<T>(this: _OverloadYield<T>, value: T, kind: Kind) {\n  this.v = value;\n  this.k = kind;\n}\n\nexport { _OverloadYield as default };\n"], "mappings": ";;;;;;AAkBA,SAASA,cAAcA,CAA6BC,KAAQ,EAAEC,IAAU,EAAE;EACxE,IAAI,CAACC,CAAC,GAAGF,KAAK;EACd,IAAI,CAACG,CAAC,GAAGF,IAAI;AACf", "ignoreList": []}