const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const { securityHeaders, corsConfig } = require('../config/security');

/**
 * 安全中间件配置
 * 包含HTTPS、安全头、CORS等安全设置
 */

/**
 * 配置Helmet安全头
 */
function configureHelmet() {
  const helmetConfig = {
    // 内容安全策略
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
        upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
      }
    },
    
    // HTTP严格传输安全
    hsts: process.env.NODE_ENV === 'production' ? {
      maxAge: securityHeaders.hsts.maxAge,
      includeSubDomains: securityHeaders.hsts.includeSubDomains,
      preload: securityHeaders.hsts.preload
    } : false,
    
    // 其他安全头
    crossOriginEmbedderPolicy: false, // 避免与某些第三方服务冲突
    crossOriginResourcePolicy: { policy: "cross-origin" },
    
    // 隐藏X-Powered-By头
    hidePoweredBy: true,
    
    // 防止点击劫持
    frameguard: { action: 'deny' },
    
    // 防止MIME类型嗅探
    noSniff: true,
    
    // 启用XSS过滤器
    xssFilter: true,
    
    // 引用者策略
    referrerPolicy: { policy: "strict-origin-when-cross-origin" }
  };
  
  return helmet(helmetConfig);
}

/**
 * 配置CORS
 */
function configureCORS() {
  return cors({
    origin: function (origin, callback) {
      // 允许的源
      const allowedOrigins = corsConfig.origin;

      // 调试信息
      console.log('CORS Debug - Origin:', origin);
      console.log('CORS Debug - Allowed Origins:', allowedOrigins);

      // 开发环境允许无origin的请求（如Postman）
      if (process.env.NODE_ENV === 'development' && !origin) {
        return callback(null, true);
      }

      // 检查origin是否在允许列表中
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('不允许的CORS源'), false);
      }
    },
    credentials: corsConfig.credentials,
    methods: corsConfig.methods,
    allowedHeaders: corsConfig.allowedHeaders,
    exposedHeaders: corsConfig.exposedHeaders,
    optionsSuccessStatus: 200 // 支持旧版浏览器
  });
}

/**
 * 强制HTTPS重定向中间件
 */
function forceHTTPS(req, res, next) {
  // 只在生产环境启用
  if (process.env.NODE_ENV !== 'production') {
    return next();
  }
  
  // 检查是否已经是HTTPS
  if (req.secure || req.headers['x-forwarded-proto'] === 'https') {
    return next();
  }
  
  // 重定向到HTTPS
  const httpsUrl = `https://${req.headers.host}${req.url}`;
  res.redirect(301, httpsUrl);
}

/**
 * 自定义安全响应头中间件
 */
function customSecurityHeaders(req, res, next) {
  // 添加自定义安全头
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

  // 移除敏感信息
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');

  next();
}

/**
 * 请求清理中间件
 */
function sanitizeRequest() {
  return [
    // MongoDB注入防护
    mongoSanitize({
      replaceWith: '_',
      onSanitize: ({ req, key }) => {
        console.warn(`检测到潜在的MongoDB注入尝试: ${key} in ${req.originalUrl}`);
      }
    }),
    
    // XSS防护
    xss()
  ];
}

/**
 * 压缩中间件
 */
function configureCompression() {
  return compression({
    // 压缩级别 (1-9, 6是默认值)
    level: 6,
    
    // 压缩阈值 (字节)
    threshold: 1024,
    
    // 过滤器 - 决定哪些响应需要压缩
    filter: (req, res) => {
      // 不压缩已经压缩的内容
      if (res.getHeader('Content-Encoding')) {
        return false;
      }
      
      // 使用默认过滤器
      return compression.filter(req, res);
    }
  });
}

/**
 * 信任代理设置
 */
function configureTrustProxy(app) {
  // 生产环境信任代理
  if (process.env.NODE_ENV === 'production') {
    app.set('trust proxy', 1); // 信任第一个代理
  }
}

/**
 * 安全中间件组合
 */
function applySecurityMiddleware(app) {
  // 信任代理设置
  configureTrustProxy(app);
  
  // 强制HTTPS（生产环境）
  app.use(forceHTTPS);
  
  // 压缩
  app.use(configureCompression());
  
  // 安全头
  app.use(configureHelmet());
  app.use(customSecurityHeaders);
  
  // CORS
  app.use(configureCORS());
  
  // 请求清理
  app.use(sanitizeRequest());
  
  console.log('✅ 安全中间件已配置');
}

/**
 * 生产环境HTTPS服务器配置
 */
function createHTTPSServer(app) {
  if (process.env.NODE_ENV !== 'production') {
    return null;
  }
  
  const https = require('https');
  const fs = require('fs');
  const path = require('path');
  
  try {
    // SSL证书路径
    const certPath = process.env.SSL_CERT_PATH || '/etc/ssl/certs/server.crt';
    const keyPath = process.env.SSL_KEY_PATH || '/etc/ssl/private/server.key';
    const caPath = process.env.SSL_CA_PATH; // 可选的CA证书
    
    // 检查证书文件是否存在
    if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
      console.warn('⚠️  SSL证书文件不存在，跳过HTTPS服务器创建');
      return null;
    }
    
    // SSL选项
    const sslOptions = {
      cert: fs.readFileSync(certPath),
      key: fs.readFileSync(keyPath)
    };
    
    // 添加CA证书（如果存在）
    if (caPath && fs.existsSync(caPath)) {
      sslOptions.ca = fs.readFileSync(caPath);
    }
    
    // 创建HTTPS服务器
    const httpsServer = https.createServer(sslOptions, app);
    
    console.log('✅ HTTPS服务器已创建');
    return httpsServer;
    
  } catch (error) {
    console.error('❌ 创建HTTPS服务器失败:', error.message);
    return null;
  }
}

/**
 * HTTP到HTTPS重定向服务器
 */
function createRedirectServer() {
  if (process.env.NODE_ENV !== 'production') {
    return null;
  }
  
  const http = require('http');
  
  const redirectServer = http.createServer((req, res) => {
    const httpsUrl = `https://${req.headers.host}${req.url}`;
    res.writeHead(301, { Location: httpsUrl });
    res.end();
  });
  
  console.log('✅ HTTP重定向服务器已创建');
  return redirectServer;
}

module.exports = {
  applySecurityMiddleware,
  createHTTPSServer,
  createRedirectServer,
  forceHTTPS,
  configureHelmet,
  configureCORS,
  customSecurityHeaders,
  sanitizeRequest,
  configureCompression
};
